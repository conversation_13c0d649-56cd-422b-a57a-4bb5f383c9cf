import { useState } from "react";
import { getCookie, setCookie } from "cookies-next";
import axios from "axios";
import { useTranslations } from "next-intl";
import { message } from 'antd';

export  function useCreateUser() {
  const [loading, setLoading] = useState(false);
  const t = useTranslations();

  
  function createUser(data: any, reset: any, setActive: any) {
    if (data.password !== data.confirmPassword) {
      // 使用 message 替代 messageApi
      message.error(t("message.8810964afa6a6d413988f9e7567bec617ecc"));
      return;
    }
    
    setLoading(true);
    let newdata={
      email: data.email,
      password: data.password,
      channel: 'default-channel',
      locale:data.locale,
      firstName: data.firstName,
      lastName: data.lastName,
      phone: data.phone,
      position: data.position,
      companyName: data.companyName,
    }

    // 打印前端发送的注册数据
    console.log("=== 前端发送的注册数据 ===");
    console.log("表单原始数据:", JSON.stringify(data, null, 2));
    console.log("发送给后端的数据:", JSON.stringify(newdata, null, 2));
    console.log("========================");

    axios.post("/api/auth/register", newdata)
      .then((response) => {
        // 打印后端返回的响应数据
        console.log("=== 后端返回的响应数据 ===");
        console.log("完整响应:", JSON.stringify(response.data, null, 2));
        console.log("========================");

        if(response.data.code==200){
          message.success(t("message.e799bac7bf23be4445b8f7cf50f6b78485e8"));
          reset();

          
          if (!getCookie("signup__user__info")) {
            const userInfo = {
              username: response.data.data?.user?.email || response.data.username,
              email: response.data.data?.user?.email || response.data.email,
              id: response.data.data?.user?.id || response.data.id,
              firstName: response.data.data?.user?.firstName,
              lastName: response.data.data?.user?.lastName,
              phone: response.data.data?.additionalInfo?.phone,
              position: response.data.data?.additionalInfo?.position,
              companyName: response.data.data?.additionalInfo?.companyName,
            };
            setCookie("created__user__info", userInfo);
          }
          // setTimeout(() => {
          //   setActive(1);
          // }, 1000);
        }else{
          message.error(response.data.msg);
        }


        setLoading(false);

      })
      .catch((error) => {
        if (!error?.response?.data?.code) {
          return message.error(t("message.68c53dd9a67388497e1b48c6b208e75fd369"));
        }
        // 使用 switch 语句使代码更清晰
        switch(error.response.data.code) {
          case "registration-error-email-exists":
          case "registration-error-username-exists":
            message.error(t("message.31a9d22b1c535c4d0598eadaef98b0faa182"));
            break;
          case "customer_invalid_email":
            message.error(t("message.921f2b497830d143aacbf3ab15ec70888bce"));
            break;
          case "service-error":
            message.error(t("message.4aa583388500d7427b48f82c2dd496909f12"));
            break;
        }
        setLoading(false);
      });
  }

  return { createUser, loading };
}
