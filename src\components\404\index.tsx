import { useTranslations } from "next-intl";
import Image from "next/image";
import { Link } from "@/navigation.ts";

export const NotFouned=({link}:{ link?: string })=> {
	const t=useTranslations()
	return <div className="container ">
		<Image src="/image/404.png" width={1500} height={1000} alt={"404"} className="object-cover mx-auto w-fit "></Image>
		<div className="text-center mb-40">
			<p className="text-xl font-bold py-5">{t('common.notFound')}</p>
			<Link href={`${link ? link :'/'}`}
						className="border border-main px-4 py-2 text-lg rounded-md bg-main text-white ">{t('common.return')}</Link>
		</div>

	</div>
}
