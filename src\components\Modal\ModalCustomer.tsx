'use client'
import React, { useEffect, useRef, useState } from "react";

import { useModalCustomerContext } from '@/context/CustomerContext'
import { useKfStore } from "@/lib/store/customer";
import { Avatar, Button, Input } from "antd";
import { RingLoader } from "react-spinners";
import { useTranslations } from "next-intl";
import { CloseCircleTwoTone, SendOutlined } from "@ant-design/icons";
import Image from "next/image";
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';

const Robot = ({ content }: {
	content: string
}) => {
	return (
		<div className="flex w-full py-3 px-4 !items-start animate__animated animate__fadeInLeft">
			<div className="flex-shrink-0 relative">
                          <Image
                            src={ "/image/logo.png" }
                            width={50}
                            height={50}
                            alt="logo"
                            className="border-2 border-white shadow-sm p-1"
                          ></Image>
				<div className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white"></div>
			</div>
			<div className="flex flex-col gap-1 ml-3 max-w-[80%]">
				<span className="text-xs text-gray-500">AI Assistant</span>
				<div className="py-2.5 px-4 rounded-2xl ignore !text-gray-800 bg-gray-100 leading-relaxed shadow-sm">
        <ReactMarkdown rehypePlugins={[rehypeRaw]}>
        {`${content}`}
      </ReactMarkdown>

				</div>
			</div>
		</div>
	);
};

const Me = ({ content }: {
	content: string
}) => {
	return (
		<div className="flex py-3 px-4 !items-start justify-end animate__animated animate__fadeInRight">
			<div className="flex flex-col gap-1 items-end max-w-[80%]">
				<span className="text-xs text-gray-500">You</span>
				<div className="py-2.5 px-4 rounded-2xl ignore !text-white bg-gradient-to-r from-blue-500 to-indigo-600 leading-relaxed shadow-md">
					{content}
				</div>
			</div>
		</div>
	);
};

const SaveEmail = ({
	setShowSaveEmail
}: {
	setShowSaveEmail: (show: boolean) => void
}) => {
	const t = useTranslations()
	const { setEmail } = useKfStore();
	const [value, setValue] = useState("");
	const [errMsg, setErrMsg] = useState("");

	const formatEmail = (email: string) => {
		const reg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+((\.[a-zA-Z0-9_-]{2,3}){1,2})$/;
		return reg.test(email);
	};

	useEffect(() => {
		setErrMsg(!formatEmail(value) ? t('base.vailEmailTip') : "");
	}, [value]);

	return (
		<div className="flex flex-col items-center justify-center min-h-[400px] px-8">
			<div className="w-full max-w-md bg-white rounded-2xl p-8  transform transition-all">
				<div className="text-center mb-8">
					<div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
						<svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
						</svg>
					</div>
					<h3 className="text-2xl font-bold bg-gradient-to-r from-blue-500 to-indigo-600 text-transparent bg-clip-text mb-2">
						{t('base.contactYou')}
					</h3>
					<p className="text-gray-500 text-sm">
						{t('base.ContactYouContent')}
					</p>
				</div>
				
				<div className="space-y-4">
					<div className="relative">
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
							</svg>
						</div>
						<input
							type="email"
							name="email"
							value={value}
							onChange={(e) => setValue(e.target.value)}
							id="email"
							className={`block w-full pl-10 pr-4 py-3 border ${errMsg ? 'border-red-300' : 'border-gray-200'} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-gray-50/50`}
							placeholder="<EMAIL>"
							aria-describedby="email-description"
						/>
					</div>
					
					{errMsg && (
						<div className="bg-red-50 text-red-500 text-sm rounded-lg p-3 flex items-center gap-2">
							<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-4 h-4 flex-shrink-0">
								<path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
							</svg>
							{errMsg}
						</div>
					)}
					
					<button
						onClick={() => {
							if (value && formatEmail(value)) {
								setEmail(value);
								setShowSaveEmail(false);
							}
						}}
						className="w-full py-3 px-4 text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl font-medium hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-blue-500/25 transform hover:-translate-y-0.5"
					>
						{t('base.Save')}
					</button>
				</div>
			</div>
		</div>
	);
};

const ModalCustomer = () => {
	const t=useTranslations()

	const { isCustomerModalOpen, closeModalCustomer } = useModalCustomerContext();
	const { email, sendMessage,getChatToken } = useKfStore();
	const inputRef = useRef(null);
	const chatRef = useRef(null);
	const [showSaveEmail, setShowSaveEmail] = useState<boolean>(false);
	const [chatMsg, setChatMsg] = useState<{
		type: "robot" | "me",
		msg: string;
		date: Date
	}[]>([{
		type: "robot",
		msg: "Hello! What do you need help with?",
		date: new Date()
	}]);
	const [loading, setLoading] = useState<boolean>(false);
	useEffect(() => {
		email&&getChatToken();
	}, []);
	useEffect(() => {
		if (isCustomerModalOpen) {
			inputRef.current?.focus();
		}
		setShowSaveEmail(!email);
		// setOverflow(open ? "hidden" : "auto");
		document.documentElement.style.overflow = isCustomerModalOpen ? "hidden" : "auto";

	}, [isCustomerModalOpen]);

	const handleSetChatMsg = (type: "robot" | "me", msg: string) => {
		// @ts-ignore
		setChatMsg(old => {
			const value = JSON.parse(JSON.stringify(old));
			// @ts-ignore
			value.push({
				type: type,
				msg: msg,
				date: new Date()
			});
			return value;
		});
	};

	const handleSend = async () => {
		const msg = inputRef.current?.value;
		if (msg) {
			inputRef.current.value = "";
			handleSetChatMsg("me", msg);
			const result = await sendMessage(msg, setLoading);
			if (result) {
				handleSetChatMsg("robot", result);
			} else {
				handleSetChatMsg("robot", "Error");
			}
		}
	};

	useEffect(() => {
		chatRef.current?.scrollTo({ top: chatRef.current.scrollHeight });
	}, [chatMsg]);

	return (
		<div className={`ai z-50 md:bottom-5 md:left-5 bottom-14 max-md:bottom-20 ${isCustomerModalOpen ? "fixed" : "hidden"} animate__animated animate__fadeInUp`}>
			<div className="md:w-[400px] shadow-xl md:h-[600px] w-[100vw] bg-white border rounded-2xl overflow-hidden">
				<div className="h-[60px] flex justify-between items-center px-6 text-white font-medium bg-gradient-to-r from-blue-500 to-indigo-600">
					<div className="flex items-center gap-3">
						<div className="flex items-center gap-2">
							<div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
							<span className="text-lg font-medium">{t('base.customerAssistant')}</span>
						</div>
					</div>
					<div 
						onClick={closeModalCustomer} 
						className="cursor-pointer p-2 hover:bg-white/10 rounded-full transition-colors h-[20px] box-content"
					>
						<CloseCircleTwoTone className="text-xl text-white" />
					</div>
				</div>
				<style>
					{`
						.online-chat {
							scrollbar-width: thin;
							scrollbar-color: #e2e8f0 transparent;
						}
						.online-chat::-webkit-scrollbar {
							width: 5px;
						}
						.online-chat::-webkit-scrollbar-track {
							background: transparent;
						}
						.online-chat::-webkit-scrollbar-thumb {
							background: #e2e8f0;
							border-radius: 10px;
						}
						.online-chat::-webkit-scrollbar-thumb:hover {
							background: #cbd5e1;
						}
						.message-input::placeholder {
							opacity: 0.5;
						}
					`}
				</style>
				<div className="h-[480px] max-md:h-[420px] overflow-y-auto overflow-x-hidden online-chat bg-white" ref={chatRef}>
					{showSaveEmail ? (
						<SaveEmail setShowSaveEmail={setShowSaveEmail} />
					) : (
						<>
							{chatMsg.map((item, index) => {
								if (item.type === "robot") {
									return <Robot content={item.msg} key={index} />;
								} else {
									return <Me content={item.msg} key={index} />;
								}
							})}
						</>
					)}
				</div>
				{!showSaveEmail && (
					<div className="border-t flex justify-between items-center bg-gray-50/80 px-4">
						<input 
							type="text" 
							className="message-input flex-1 h-[60px] text-[16px] border-none text-gray-700 bg-transparent placeholder:text-gray-500 focus:outline-none focus:ring-0 px-2"
							placeholder={t('message.7f430b484c41ca46468b6fea61903af4de66')} 
							ref={inputRef} 
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									handleSend();
								}
							}} 
						/>
						{loading ? (
							<div className="mr-2">
								<RingLoader className="!text-blue-500" size={20} />
							</div>
						) : (
							<button 
								onClick={handleSend}
								className="p-2.5 hover:bg-blue-500/10 rounded-full transition-colors duration-200 group"
							>
								<SendOutlined className="cursor-pointer text-blue-500 text-xl group-hover:scale-110 transition-transform duration-200" />
							</button>
						)}
					</div>
				)}
			</div>
		</div>
	)
}

export default ModalCustomer
