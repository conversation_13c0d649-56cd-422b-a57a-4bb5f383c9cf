"use client";
import React, { useState, useEffect } from 'react';
import { HomeTaile } from "@/components/Contact/ConcatPage";
import { useTranslations } from 'next-intl';
import { Drawer } from 'antd';
import useIsMobile from '@/lib/hooks/useIsMobile';
import Categories from "@/components/ProductList/Categories";
import { SearchBar } from "../SearchBar/page";
import { useBreakpointColumns } from '@/lib/store/breakpointColumnsObj';
import { usePathname } from 'next/navigation';
import { defaultLocale } from '@/config';
import { BodyText } from "@/components/BodyText";
import InspirationCubeLayout from './InspirationCubeLayout';
function Index({ children, channel, locale, categoriesData }) {
  const t = useTranslations();
  const [open, setOpen] = useState(false);
  const pathname = usePathname();
  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  let isMobile = useIsMobile()


  let { changeBreakpointColumns, col, setcol } = useBreakpointColumns()

  function changeBreakpointColumnsObj(col: number) {
    changeBreakpointColumns(col)
    setcol(col)
  }
  const [pageTitle, setPageTitle] = useState(t('common.Products'));
  const [pageDescription, setPageDescription] = useState<any>('');

  useEffect(() => {
    console.log("categoriesData----", categoriesData)
    // 检查当前路径是否为产品分类页面
    const productsPathMatch = pathname.match(/\/products\/([^/]+)/);

    if (productsPathMatch && productsPathMatch[1]) {
      const slug = productsPathMatch[1];

      // 检查是否为 inspiration-cube 特殊页面
      if (slug === 'inspiration-cube') {
        setPageTitle('Inspiration Cube');
        setPageDescription('Discover our curated collection of inspiring products');
        return;
      }

      // 从 categoriesData 数据中查找匹配的 slug
      if (categoriesData && categoriesData.edges && categoriesData.edges.length > 0) {
        // 扁平化所有分类节点以便于查找
        let allCategories = [];
        const flattenCategories = (categories) => {
          if (!categories) return;

          categories.forEach(category => {
            if (category && category.node) {
              allCategories.push(category.node);

              // 递归处理子分类
              if (category.node.children &&
                category.node.children.edges &&
                category.node.children.edges.length > 0) {
                flattenCategories(category.node.children.edges);
              }
            }
          });
        };

        flattenCategories(categoriesData.edges);

        // 查找匹配的分类
        const matchedCategory = allCategories.find(cat =>
          cat.slug === slug ||
          (cat.translation && cat.translation.slug === slug)
        );

        if (matchedCategory) {
          // 如果找到匹配的分类，使用其名称作为标题
          const categoryName = locale === defaultLocale ? matchedCategory.name : matchedCategory.translation ?
            matchedCategory.translation.name :
            (matchedCategory.name || t('common.Products'));

          // 获取分类描述并解析 JSON 格式（参考 Information.tsx 的逻辑）
          const rawDescription = locale === defaultLocale ?
            matchedCategory.description :
            (matchedCategory.translation?.description || matchedCategory.description || '');

          // 解析描述 JSON 数据
          let parsedDescription = null;
          if (rawDescription) {
            try {
              parsedDescription = JSON.parse(rawDescription);
            } catch (error) {
              console.log('Failed to parse category description JSON:', error);
            }
          }

          setPageTitle(categoryName);
          setPageDescription(parsedDescription);
        } else {
          // 如果在 categoriesData 中找不到匹配的分类，使用默认标题
          setPageTitle(t('common.Products'));
          setPageDescription(null);
        }
      } else {
        setPageTitle(t('common.Products'));
        setPageDescription(null);
      }
    } else {
      // 如果不是产品分类页面，使用默认标题
      setPageTitle(t('common.Products'));
      setPageDescription(null);
    }
  }, [pathname, t, categoriesData]);

  // 检查是否为 inspiration-cube 页面
  const productsPathMatch = pathname.match(/\/products\/([^/]+)/);
  const isInspirationCube = productsPathMatch && productsPathMatch[1] === 'inspiration-cube';

  // 如果是 inspiration-cube 页面，使用特殊布局
  if (isInspirationCube) {
    return (
      <InspirationCubeLayout
        channel={channel}
        locale={locale}
        categoriesData={categoriesData}
      >
        {children}
      </InspirationCubeLayout>
    );
  }

  return (
    <>
      {/* <HomeTaile msg={t('common.Product')} /> */}

      {/* DJI风格的产品页面布局 */}
      <section className="min-h-screen bg-[#f5f5f7]">
        {/* 分类标题和描述 */}
        <div className="py-[64px] mb-[20px]">
          <div className="max-w-[1200px] mx-auto px-4 text-center">
            <h1 className="text-[40px] font-bold text-[#242424] mb-6 tracking-tight">
              {pageTitle}
            </h1>

            {/* 分类描述 */}
            {pageDescription?.blocks?.[0]?.data?.text && (
              <BodyText
                innerHTML={pageDescription.blocks[0].data.text}
                className="text-[16px] text[#242424] mx-auto leading-relaxed"
              />
            )}
          </div>
        </div>
        <div className="!max-w-[1200px] mx-auto px-4">
          {/* 移动端分类按钮 */}
          <div className="mb-6 md:hidden">
            <button
              onClick={showDrawer}
              className="flex w-full items-center gap-3 px-4 py-3 transition-all duration-300"
            >
              <GETsvg />
              <span className="font-medium text-gray-700">{t('common.Categories')}</span>
            </button>
          </div>

          {/* 桌面端布局 */}
          <div className="flex gap-8 max-md:flex-col">

            {/* 左侧分类栏 - DJI风格 */}
            <div className="w-80 shrink-0 max-md:hidden">
              <div className="space-y-6">
                {/* 动态分类标题
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {t('common.Product')}
                  </h2>
                  <p className="text-gray-600 text-sm">
                    {t('common.Categories')}
                  </p>
                </div> */}

                {/* DJI风格分类列表 */}
                <div className="">
                  <Categories locale={locale} categoriesData={categoriesData} />
                </div>

                {/* 搜索栏 */}
                {/* <div className="bg-white rounded-2xl p-6 shadow-sm">
                  <SearchBar channel={channel} />
                </div> */}
              </div>
            </div>

            {/* 右侧产品内容区域 */}
            <div className="flex-1 min-w-0">
              {/* 产品网格 */}
              <div className="w-full">
                {children}
              </div>
            </div>
          </div>
        </div>
      </section>
      <Drawer
        title={t('common.Categories')}
        placement={"left"}
        onClose={onClose}
        open={open}
        key={"left"}
        width={isMobile ? "86vw" : '30vw'}
      >
        {/* <SearchBar channel={channel} /> */}
        <Categories locale={locale} categoriesData={categoriesData} />
      </Drawer>

    </>
  );
}

export default React.memo(Index);

function GETsvg() {
  return (
    <svg width="20" height="12" viewBox="0 0 20 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 1C0 0.734784 0.105357 0.48043 0.292893 0.292893C0.48043 0.105357 0.734784 0 1 0H19C19.2652 0 19.5196 0.105357 19.7071 0.292893C19.8946 0.48043 20 0.734784 20 1C20 1.26522 19.8946 1.51957 19.7071 1.70711C19.5196 1.89464 19.2652 2 19 2H1C0.734784 2 0.48043 1.89464 0.292893 1.70711C0.105357 1.51957 0 1.26522 0 1ZM3 6C3 5.73478 3.10536 5.48043 3.29289 5.29289C3.48043 5.10536 3.73478 5 4 5H16C16.2652 5 16.5196 5.10536 16.7071 5.29289C16.8946 5.48043 17 5.73478 17 6C17 6.26522 16.8946 6.51957 16.7071 6.70711C16.5196 6.89464 16.2652 7 16 7H4C3.73478 7 3.48043 6.89464 3.29289 6.70711C3.10536 6.51957 3 6.26522 3 6ZM8 10C7.73478 10 7.48043 10.1054 7.29289 10.2929C7.10536 10.4804 7 10.7348 7 11C7 11.2652 7.10536 11.5196 7.29289 11.7071C7.48043 11.8946 7.73478 12 8 12H12C12.2652 12 12.5196 11.8946 12.7071 11.7071C12.8946 11.5196 13 11.2652 13 11C13 10.7348 12.8946 10.4804 12.7071 10.2929C12.5196 10.1054 12.2652 10 12 10H8Z"
        fill="currentColor"
      ></path>
    </svg>
  );
}

function GETsvg2({ color = "#a1a1a1" }: { color?: string }) {
  return (
    <svg width="13" height="13" viewBox="0 0 13 13" fill="none">
      <circle cx="2.4375" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="10.5625" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="2.4375" cy="10.5625" r="2.4375" fill={color}></circle>
      <circle cx="10.5625" cy="10.5625" r="2.4375" fill={color}></circle>
    </svg>
  );
}

function GETsvg3({ color = "#a1a1a1" }: { color?: string }) {
  return (
    <svg width="22" height="13" viewBox="0 0 22 13" fill="none">
      <circle cx="2.4375" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="10.5625" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="18.6875" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="2.4375" cy="10.5625" r="2.4375" fill={color}></circle>
      <circle cx="10.5625" cy="10.5625" r="2.4375" fill={color}></circle>
      <circle cx="18.6875" cy="10.5625" r="2.4375" fill={color}></circle>
    </svg>
  );
}

function GETsvg4({ color = "#a1a1a1" }: { color?: string }) {
  return (
    <svg width="30" height="13" viewBox="0 0 30 13" fill="none">
      <circle cx="2.4375" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="10.5625" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="18.6875" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="26.8125" cy="2.4375" r="2.4375" fill={color}></circle>
      <circle cx="2.4375" cy="10.5625" r="2.4375" fill={color}></circle>
      <circle cx="10.5625" cy="10.5625" r="2.4375" fill={color}></circle>
      <circle cx="18.6875" cy="10.5625" r="2.4375" fill={color}></circle>
      <circle cx="26.8125" cy="10.5625" r="2.4375" fill={color}></circle>
    </svg>
  );
}
