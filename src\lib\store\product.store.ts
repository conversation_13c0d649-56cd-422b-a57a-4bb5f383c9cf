import { create } from "zustand";
import { persist } from "zustand/middleware";

type State = {
  currencyUnit: string,
  setCurrencyUnit: (unit: string) => void,

}

export const useProductStore = create(
  persist<State>(
    (set, get) => ({
      currencyUnit: "USD",// 货币初始单位
      setCurrencyUnit: (unit: string) => set({ currencyUnit: unit }), // 更新货币单位的方法

    }),
    {
      name: "productState"
    }
  )
);