"use client";
import { defaultLocale } from "@/config";
import { Tabs } from "antd";
import { StyledArticle } from "../Blogs/BlogArticle";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { contactInfo } from "@/lib/contacts";
import { motion } from "framer-motion";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import ProductReviews from "./product-reviews";
const ProductDescription = ({ product, locale }) => {
	const t = useTranslations("nav");

	const tabData = [
		{
			label: t("Description"),
		},
		{
			label: t("Additional Information"),
		},
		{
			label: t("Privacy Policy"),
		},
		{
			label: t("Reviews"),
		},
	];
	const [activeTab, setActiveTab] = useState(0); // Default to "Return Policies"

	let { variants } = product;
	const attributes = product.attributes.map((item) => {
		return {
			label: item.attribute?.translation?.name || item.attribute.name,
			value: item.values.map((i) => i.translation?.name || i.name).join(", "),
		};
	});
	return (
		<>
			<div className="ProductDescription containernone mx-auto  rounded-sm border-[1px] border-[#ebebeb]  py-8">
				<div className="flex space-x-8 border-b-[1px] border-[#ebebeb] px-4 max-md:flex-col max-md:space-x-0 max-md:space-y-1 max-md:py-2">
					{tabData.map((tab, index) => (
						<div
							key={index}
							className={`relative cursor-pointer pb-4 hover:text-gray-900 max-md:pb-0 ${
								activeTab === index ? "text-gray-900 max-md:inline-block max-md:!text-[#3086c8]" : ""
							}`}
							onClick={() => setActiveTab(index)}
						>
							{tab.label}
							{/* 底部动态边框 */}
							<div
								className={`absolute bottom-0 left-0 h-[2px] bg-gray-900 transition-all duration-300 ${
									activeTab === index ? "w-full max-md:w-0" : "w-0"
								}`}
							/>
						</div>
					))}
				</div>

				{/* Content */}
				{activeTab == 0 && (
					<StyledArticle>
						<div
							className="p-4"
							dangerouslySetInnerHTML={{
								__html:
									locale == defaultLocale
										? filterSortDsc(product.descriptionJson).longDsc
										: filterSortDsc(product.translation.descriptionJson).longDsc ||
											filterSortDsc(product.descriptionJson).longDsc,
							}}
						/>
					</StyledArticle>
				)}

				{activeTab == 1 && (
					<div className="w-full overflow-hidden p-8 max-md:p-2">
						<table className="w-full overflow-hidden">
							<tr className="flex">
								<td className="w-[25%] font-medium text-[#868686] max-md:w-[5%]">{t("Variants")}</td>
								<td className="line-clamp-1 flex-1 text-[#868686]">
									{variants.map((item, index) => (
										<span key={item.id}>
											{item?.translation?.name || item?.name}
											{index < variants.length - 1 && ","}
										</span>
									))}
								</td>
							</tr>

							{attributes &&
								attributes?.map((item, i) => {
									return (
										<tr key={i} className="flex w-full">
											<td className="w-[25%] font-medium text-[#868686] max-md:w-[5%]">{item.label}</td>
											<td className="flex-1 text-[#868686]">{item.value}</td>
										</tr>
									);
								})}
						</table>
					</div>
				)}

				{activeTab == 2 && (
					<div className="w-full p-8">
						<h2 className="mb-6 text-2xl font-medium text-gray-900">{t("Privacy Policy")}</h2>
						{/* <p className="leading-relaxed text-gray-600">{t("Atyour")}</p> */}

						<p className="leading-relaxed text-gray-600">{t("We collect")}</p>

						<p className="leading-relaxed text-gray-600">{t("We implement")}</p>

						<p className="leading-relaxed text-gray-600">
							{t("By using our")}
							<a href={`mailto:${contactInfo.email}`} className="text-blue-500">
								{" "}
								{contactInfo.email}
							</a>
							.
						</p>
					</div>
				)}

				{/* 商品评价 */}
				{activeTab == 3 && (
					<div className="w-full p-8 max-md:px-4">
						<ProductReviews productId={product.id} />
					</div>
				)}
			</div>
		</>
	);
};

export default ProductDescription;

type dscList = {
	blocks: dscObj[];
};
type dscObj = {
	data: { text: string };
	type: string;
};
const filterSortDsc = (dsc: string) => {
	if (!dsc) {
		dsc = '{"blocks": []}';
	}

	const dscObj = JSON.parse(dsc) as dscList;
	let sortDsc = "";
	let longDsc = "";

	// 打印后立即检查blocks是否存在
	// console.log(dscObj, 'dscObj');

	// 首先检查blocks是否存在
	if (!dscObj.blocks) {
		// 如果blocks不存在，初始化为空数组
		dscObj.blocks = [];
	}

	// 现在可以安全地访问length属性
	if (dscObj.blocks.length > 0) {
		// 检查索引1是否存在
		if (dscObj.blocks.length > 1 && dscObj.blocks[1]?.data?.text) {
			sortDsc = dscObj.blocks[1].data.text;
		}

		// 检查索引0是否存在
		if (dscObj.blocks[0]?.data?.text) {
			longDsc = handlerInnerHtml(dscObj.blocks[0].data.text);
		}
	}

	return { sortDsc, longDsc };
};
const handlerInnerHtml = (html: string): string => {
	if (!html) return html;
	return html
		.replace(/&lt;/g, "<")
		.replace(/&gt;/g, ">")
		.replace(/&amp;/g, "&")
		.replace(/&quot;/g, '"')
		.replace(/&#8216;/g, "‘")
		.replace(/&#8217;/g, "’")
		.replace(/&#8211;/g, "–")
		.replace(/&nbsp;/g, " ");
};
