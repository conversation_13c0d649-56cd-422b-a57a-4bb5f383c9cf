/* top nav header */
.top-nav {
	position: fixed;
	z-index: 100 !important;
width: 100%;
	.choose-type {
		cursor: pointer;

		.list-option {
			position: absolute;
			top: 100%;
			left: -47px;
			transform: translateY(20px);
			opacity: 0;
			visibility: hidden;
			transition: 0.4s;
			z-index: 101;
			box-shadow: 0px 4px 10px 3px rgba(0, 0, 0, 0.15);

			&::before {
				content: "";
				position: absolute;
				width: 12px;
				height: 12px;
				background-color: var(--white);
				transform: translateX(-50%) rotate(45deg);
				left: 50%;
				top: -5px;
				z-index: -1;
			}

			li {
				padding: 8px 16px;
				cursor: pointer;
				transition: 0.3s;

				&:hover {
					background: rgba($color: #3383ff, $alpha: 0.6);
					color: black;
					border:1px solid;
				}
			}

			&.open {
				transform: translateY(10px);
				opacity: 1;
				visibility: visible;
			}
		}
    .list-option2 {
			position: absolute;
			top: 100%;
			left: 25px;
			transform: translateY(20px);
			opacity: 0;
			visibility: hidden;
			transition: 0.4s;
			z-index: 101;
			box-shadow: 0px 4px 10px 3px rgba(0, 0, 0, 0.15);

			&::before {
				content: "";
				position: absolute;
				width: 12px;
				height: 12px;
				background-color: var(--white);
				transform: translateX(-50%) rotate(45deg);
				left: 50%;
				top: -5px;
				z-index: -1;
			}

			li {
				padding: 8px 16px;
				cursor: pointer;
				transition: 0.3s;

				&:hover {
					background: rgba($color: #3383ff, $alpha: 0.6);
					color: black;
					border:1px solid;
				}
			}

			&.open {
				transform: translateY(10px);
				opacity: 1;
				visibility: visible;
			}
		}
	}
}

/* menu header */
.header-menu {
	position: relative;
	z-index: 101;
	transition: all ease 0.5s;

	&.fixed {
		// background-color: var(--white);
		box-shadow: 0px 10px 25px 0px rgba(43, 52, 74, 0.12);
		top: 100px;
		animation: animateHeader ease 0.5s;

		&.style-watch {
			background-color: var(--green);
		}
	}
}

@keyframes animateHeader {
	0% {
		transform: translate3d(0, -100%, 0);
	}

	100% {
		transform: none;
	}
}

.menu-main {
	ul li {
		a,
		.link {
			position: relative;
			cursor: pointer;

			&::before {
				content: "";
				position: absolute;
				bottom: 0;
				left: 0;
				width: 0;
				height: 1px;
				background-color: var(--black);
				transition: all ease 0.3s;
			}

			&.active {
				color: var(--black);
			}

			&.view-all-btn::before {
				width: 100%;
				height: 2px;
				background-color: var(--secondary);
			}
		}

		> a.active::before,
		> .link.active::before {
			width: 100%;
		}

		&.logo a::before,
		&.logo .link::before {
			display: none;
		}

		&:hover {
			> a.view-all-btn::before,
			> .link.view-all-btn::before {
				background-color: var(--black);
			}
		}
	}

	.sub-menu,
	.mega-menu {
		z-index: 1;
		opacity: 0;
		visibility: hidden;
		transition: all ease 0.3s;
		transform: scaleY(0);
		transform-origin: top center;
		box-shadow: 0px 5px 25px 0px rgba(138, 159, 168, 0.25);

		li a,
		li .link {
			padding: 7px 0;
			white-space: nowrap;
			//display: inline-block;
			text-transform: capitalize;
		}

		li:hover a,
		li:hover .link {
			color: var(--black);

			&::before {
				width: 100%;
			}
		}
	}

	.mega-menu {
		.banner-ads-block {
			.banner-ads-item {
				cursor: pointer;

				img {
					transition: 0.7s;
				}

				&:hover {
					img {
						transform: scale(1.1);
					}
				}
			}
		}
	}

	> ul > li {
		> a::before,
		> .link::before {
			bottom:6px;
			height: 2px;
		}

		.sub-menu {
			&:hover {
				opacity: 1;
				visibility: visible;
				transform: scaleY(1);
			}
		}

		&:hover {
			.sub-menu,
			.mega-menu {
				opacity: 1;
				visibility: visible;
				transform: scaleY(1);
			}

			> a::before,
			> .link::before {
				width: 100%;
			}
		}
	}

	&.style-eight {
		> ul > li > a::before,
		> ul > li > .link::before {
			bottom: 10px;
		}
	}

	.product-item .list-action {
		grid-template-rows: repeat(2, minmax(0, 1fr));
		grid-template-columns: unset;
		transform: translateY(120px);
		gap: 8px;
	}
}

// menu-mobile
#menu-mobile {
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  position: fixed;
  top: 0;
  left: 0;
  right: auto;
  height: 100vh;
  width: 0;
  background-color: var(--white);
  z-index: 102;
  transition: all ease 0.5s;

	.heading,
	.form-search {
		transition: all ease 0.4s;
		opacity: 0;
	}

	.list-nav {
		> ul > li {
			transition: all ease 0.4s;
			opacity: 0;
		}

		.view-all-btn {
			display: inline-block;
			padding-bottom: 6px;
		}
	}

	&.open {
    opacity: 1;
    visibility: visible;
    pointer-events: unset;
    width: 80%;

		.heading,
		.form-search {
			opacity: 1;
			transition-delay: 0.5s;
		}

		.list-nav {
			> ul {
				position: relative;

				> li {
					opacity: 1;

					&:nth-child(1) {
						transition-delay: 0.6s;
					}

					&:nth-child(2) {
						transition-delay: 0.7s;
					}

					&:nth-child(3) {
						transition-delay: 0.8s;
					}

					&:nth-child(4) {
						transition-delay: 0.9s;
					}

					&:nth-child(5) {
						transition-delay: 1s;
					}

					&:nth-child(6) {
						transition-delay: 1.1s;
					}

					.sub-nav-mobile {
						position: absolute;
						top: 0;
						right: -100%;
						width: 100%;
						background-color: var(--white);
						transition: all ease 0.3s;
						z-index: 10;

						.list-nav-item {
							overflow-x: hidden;
							height: max-content;
							max-height: calc(100vh - 120px);
						}

						li {
							padding: 6px 0;

							a {
								padding: 6px 0;
							}

							.link {
								text-transform: capitalize;
							}
						}
					}

					&.open {
						.sub-nav-mobile {
							right: 0;
						}
					}
				}
			}
		}

		.mobileIcon{
			transition: all ease 0.4s;
			transition-delay: 1.2s;
		}
		.mobileContent{
			transition: all ease 0.4s;
			transition-delay: 1.3s;
		}

    .Mobilefooter{
      transition: all ease 0.4s;
			transition-delay: 1.4s;
    }
	}
}


// menu-department
.menu-department-block {
	.sub-menu-department {
		transition: all ease 0.5s;
		padding: 0 20px;
		max-height: 0;
		overflow: hidden;

		&.open {
			max-height: 500px;
			padding: 16px 20px;
		}

		.item a {
			position: relative;

			&::before {
				content: "";
				position: absolute;
				bottom: 6px;
				left: 0;
				width: 0;
				height: 1px;
				background-color: var(--black);
				transition: all ease 0.3s;
			}

			&:hover {
				&::before {
					width: 100%;
				}
			}
		}
	}
}

#header.style-nine .sub-menu-department {
	background-color: var(--surface);
	height: 480px;
}

.style-marketplace.sub-menu-department {
	background-color: var(--white) !important;
	height: 460px !important;
	border-color: transparent;

	&.open {
		padding: 5px 24px;
		border-color: var(--line);
	}

	.item {
		position: relative;

		.name {
			line-height: 24px;
		}

		&::before {
			content: "";
			position: absolute;
			bottom: -1px;
			left: 0;
			width: 0;
			height: 1px;
			background-color: var(--black);
			transition: all ease 0.3s;
		}

		&:hover {
			&::before {
				width: 100%;
			}
		}

		&:last-child {
			&::before {
				display: none;
			}
		}
	}
}

#header.style-pet .menu-department-block .sub-menu-department {
	padding: 0;

	&.open {
		padding: 0;
		max-height: 600px;
	}

	.item {
		a {
			transition: all ease 0.3s;

			&::before {
				display: none;
			}

			&:hover {
				background-color: var(--line);
			}
		}
	}
}

// Banner top
.banner-top {
	overflow: hidden;
	max-width: 100%;
	width: 100%;
}

// slider
@media (min-width: 1024px) {
	.slider-block.style-nine {
		.slider-main {
			width: calc(100% - 212px);
		}
	}
}

.swiper-slide {
	.sub-img {
		img {
			transition: all ease 0.5s;
		}
	}

	&.swiper-slide-active {
		.slider-item {
			animation: opacityAnimate 1s ease;

			.text-content {
				.text-button-uppercase,
				.text-sub-display {
					animation: animateX 0.8s ease;
				}

				.heading1,
				.heading2,
				.text-display {
					animation: animateX 1s ease;
				}

				.button-main {
					animation: animateX 1.2s ease;
				}
			}
		}

		.sub-img {
			img {
				animation: ScaleImg 0.8s ease;
			}
		}
	}
}

.style-two {
	.swiper-slide-active {
		.slider-item {
			.text-content {
				.text-button-uppercase,
				.text-sub-display {
					animation: animateY 0.6s ease;
				}

				.heading1,
				.heading2,
				.text-display {
					animation: animateY 0.8s ease;
				}

				.body1 {
					animation: animateY 1.2s ease;
				}

				.button-main {
					animation: animateY 1.4s ease;
				}
			}
		}
	}
}

.style-five {
	.swiper-slide-active {
		.slider-item {
			.text-content {
				.text-sub-display {
					animation: animateReverseX 0.6s ease;
				}

				.heading1,
				.text-display {
					animation: animateReverseX 0.8s ease;
				}

				.button-main {
					animation: animateReverseX 1s ease;
				}
			}
		}
	}
}

@keyframes opacityAnimate {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@keyframes ScaleImg {
	0% {
		transform: scale(1.2);
	}

	100% {
		transform: scale(1);
	}
}

@keyframes animateX {
	0% {
		opacity: 0;
		transform: translateX(-120px);
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes animateReverseX {
	0% {
		opacity: 0;
		transform: translateX(120px);
	}

	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes animateY {
	0% {
		opacity: 0;
		transform: translateY(60px);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.slider-toys-kid {
	.slick-list {
		overflow: unset;
		padding: 0 !important;
		margin-left: -16px;
		margin-right: -16px;
		height: 100%;
		//cursor: grab;

		.slick-track {
			display: flex !important;
			height: 100%;

			.slick-slide {
				height: inherit !important;
				padding: 0 16px;
				outline: none !important;
				border: none !important;

				> div {
					height: 100%;

					.slider-item {
						display: flex !important;
						outline: none !important;
						border: none !important;
					}
				}
			}
		}
	}

	.slick-dots {
		bottom: 15px;

		li {
			transition: all ease 0.5s;

			button {
				border-radius: 50%;
				border: 1px solid transparent;
				padding: 4px;
				transition: all ease 0.5s;
				width: 16px;
				height: 16px;
				position: relative;

				&::before {
					font-size: 8px;
					line-height: 16px;
					width: 8px;
					height: 8px;
					opacity: 1 !important;
					color: transparent !important;
					border: 1px solid var(--black);
					border-radius: 50%;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					transition: all ease 0.5s;
				}
			}

			&.slick-active {
				button {
					border-color: var(--black);

					&::before {
						background-color: var(--black);
					}
				}
			}
		}
	}
}

.style-marketplace {
	.button-main {
		&:hover {
			background-color: var(--green);
			color: var(--black);
		}
	}
}

@media (min-width: 1024px) {
	.slider-block.style-nine {
		.slider-main {
			width: calc(100% - 212px);
		}
	}

	.slider-block.style-marketplace {
		.slider-main {
			width: calc(100% - 240px);
		}
	}
}
