"use client";

import { BodyText } from "@/components/BodyText";
import { useEffect, useState } from "react";
import Skeleton from "react-loading-skeleton";
import { Placeholder } from "@/components/Placeholder";
import { useTranslations } from "next-intl";

type Props = {
	options: any;
	changeValue: (params: any) => void;
} & any;

export default function ProductAttributes({ options, changeValue }: Props) {
	const [itemValue, setItemValue] = useState<string>("");
	const handleOnchange = (value: string) => {
		setItemValue(value);
		changeValue(value);
	};
	const t = useTranslations("nav");

	useEffect(() => {
		setItemValue(options[0]);
		changeValue(options[0]);
	}, []);
	// 进入详情页 默认选中第一个变体
	return (
		<div className="select-none py-4">
			{options ? (
				<div className="font-abeezee">
					{t("Variants")}
				</div>
			) : (
				<Skeleton width={120} height={28} />
			)}
			<div className=" mt-3 flex flex-wrap items-center gap-2.5">
				{options.map((item: any, index: number) => (
					<div
						key={index}
						className="cursor-pointer"
						onClick={() => {
							handleOnchange(item);
						}}
					>
						<BodyText
							size="xs"
							intent="semibold"
							className={`mt-1 ${
								itemValue == item
									? "border-black !bg-black text-white"
									: "border-[#DFDFDF] !bg-white text-black"
							} font-abeezee  cursor-pointer border-[1px] bg-white px-4 py-2 !text-[16px] !font-normal shadow`}
						>
							{item.name}
						</BodyText>
					</div>
				))}
			</div>
		</div>
	);
}
