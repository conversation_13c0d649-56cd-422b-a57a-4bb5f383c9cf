"use client";
import { useTranslations } from "next-intl";
import React, { useState, useEffect } from "react";
import axios from "axios";
import { App, Upload } from "antd";
import type { UploadFile } from "antd";
import { PlusOutlined } from "@ant-design/icons";

interface Review {
	product_id: string;
	comment_title: string;
	comment_content: string;
	rating: number;
	client_first_name: string;
	client_last_name: string;
	client_email: string;
	created_at?: string;
	image_urls?: string[];
}

interface FormDataType {
	comment_title: string;
	comment_content: string;
	rating: number;
	client_first_name: string;
	client_last_name: string;
	client_email: string;
}

interface ProductReviewsProps {
	productId: string;
}

function ReviewForm({ productId }: { productId: string }): JSX.Element {
  const { message } = App.useApp();
	const t = useTranslations("Review");
	const [formData, setFormData] = useState<FormDataType>({
		comment_title: "",
		comment_content: "",
		rating: 5,
		client_first_name: "",
		client_last_name: "",
		client_email: "",
	});
	const [submitting, setSubmitting] = useState(false);
	const [fileList, setFileList] = useState<UploadFile[]>([]);
	const [errors, setErrors] = useState({
		comment_title: "",
		comment_content: "",
		client_first_name: "",
		client_last_name: "",
		client_email: "",
	});

	const validateForm = () => {
		let isValid = true;
		const newErrors = {
			comment_title: "",
			comment_content: "",
			client_first_name: "",
			client_last_name: "",
			client_email: "",
		};

		if (!formData.comment_title.trim()) {
			newErrors.comment_title = t("Title is required");
			isValid = false;
		}

		if (!formData.comment_content.trim()) {
			newErrors.comment_content = t("Review content is required");
			isValid = false;
		}

		if (!formData.client_first_name.trim()) {
			newErrors.client_first_name = t("First name is required");
			isValid = false;
		}

		if (!formData.client_last_name.trim()) {
			newErrors.client_last_name = t("Last name is required");
			isValid = false;
		}

		if (!formData.client_email.trim()) {
			newErrors.client_email = t("Email is required");
			isValid = false;
		} else if (!/\S+@\S+\.\S+/.test(formData.client_email)) {
			newErrors.client_email = t("Please enter a valid email");
			isValid = false;
		}

		setErrors(newErrors);
		return isValid;
	};

	const uploadImages = async (commentId: string) => {
		if (fileList.length === 0) return;

		const formData = new FormData();
		formData.append('comment_id', commentId);
		
		// 添加所有文件到 upload_list
		fileList.forEach((file, index) => {
			if (file.originFileObj) {
				formData.append(`upload_list`, file.originFileObj);
			}
		});

		try {
			await axios.post(
				`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/saleor/comment_image_upload`,
				formData,
				{
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				}
			);
		} catch (error) {
			console.error("Error uploading images:", error);
			message.error(t("Failed234"));
		}
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!validateForm()) {
			return;
		}
		setSubmitting(true);
		try {
			const response = await axios.post(
				`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/saleor/product_comment`,
				{
					...formData,
					product_id: {
						product_id: productId,
					},
				},
			);
			if (response.data.code === 200) {
				if (fileList.length > 0) {
					await uploadImages(response.data.detail.comment_id);
				}
				message.success(t("Review234"));
				setFormData({
					comment_title: "",
					comment_content: "",
					rating: 5,
					client_first_name: "",
					client_last_name: "",
					client_email: "",
				});
				setFileList([]);
			}
		} catch (error) {
			message.error(t("Failed to submit review"));
		} finally {
			setSubmitting(false);
		}
	};

	const handleRatingChange = (newRating: number) => {
		setFormData((prev) => ({ ...prev, rating: newRating }));
	};

	const handleUploadChange = ({ fileList: newFileList }: { fileList: UploadFile[] }) => {
		setFileList(newFileList);
	};

	const beforeUpload = (file: File) => {
		const isImage = file.type.startsWith('image/');
		if (!isImage) {
			message.error(t('You23'));
		}
		const isLt5M = file.size / 1024 / 1024 < 5;
		if (!isLt5M) {
			message.error(t('Image21342'));
		}
		return isImage && isLt5M;
	};


	const uploadButton = (
		<div>
			<PlusOutlined />
			<div style={{ marginTop: 8 }}>{t("Upload")}</div>
		</div>
	);

	return (
		<div className="reviews  bg-white p-6 shadow-sm max-md:p-0">
			<h3 className="mb-4 text-xl font-semibold">{t("Write a Review")}</h3>
			<form onSubmit={handleSubmit} className="space-y-4">
				<div>
					<label className="mb-1 block text-sm font-medium text-gray-700">{t("Rating")}</label>
					<div className="flex gap-1">
						{[1, 2, 3, 4, 5].map((star) => (
							<button
								key={star}
								type="button"
								onClick={() => handleRatingChange(star)}
								className="focus:outline-none"
							>
								<i
									className={`ri-star-${formData.rating >= star ? "fill" : "line"} text-2xl text-yellow-400`}
								></i>
							</button>
						))}
					</div>
				</div>

				<div>
					<label className="mb-1 block text-sm font-medium text-gray-700">{t("Title345")}</label>
					<input
						type="text"
						required
						className={`w-full rounded-md border px-3 py-2 ${errors.comment_title ? 'border-red-500' : ''}`}
						value={formData.comment_title}
						onChange={(e) => {
							setFormData((prev) => ({ ...prev, comment_title: e.target.value }));
							if (errors.comment_title) setErrors((prev) => ({ ...prev, comment_title: '' }));
						}}
					/>
					{errors.comment_title && <p className="mt-1 text-sm text-red-500">{errors.comment_title}</p>}
				</div>

				<div>
					<label className="mb-1 block text-sm font-medium text-gray-700">{t("Review")}</label>
					<textarea
						required
						className={`h-24 w-full rounded-md border-[1px] ${errors.comment_content ? 'border-red-500' : 'border-black'} px-3 py-2`}
						value={formData.comment_content}
						onChange={(e) => {
							setFormData((prev) => ({ ...prev, comment_content: e.target.value }));
							if (errors.comment_content) setErrors((prev) => ({ ...prev, comment_content: '' }));
						}}
					></textarea>
					{errors.comment_content && <p className="mt-1 text-sm text-red-500">{errors.comment_content}</p>}
				</div>

				<div className="grid grid-cols-2 gap-4">
					<div>
						<label className="mb-1 block text-sm font-medium text-gray-700">{t("First Name")}</label>
						<input
							type="text"
							required
							className={`w-full rounded-md border px-3 py-2 ${errors.client_first_name ? 'border-red-500' : ''}`}
							value={formData.client_first_name}
							onChange={(e) => {
								setFormData((prev) => ({ ...prev, client_first_name: e.target.value }));
								if (errors.client_first_name) setErrors((prev) => ({ ...prev, client_first_name: '' }));
							}}
						/>
						{errors.client_first_name && <p className="mt-1 text-sm text-red-500">{errors.client_first_name}</p>}
					</div>
					<div>
						<label className="mb-1 block text-sm font-medium text-gray-700">{t("Last Name")}</label>
						<input
							type="text"
							required
							className={`w-full rounded-md border px-3 py-2 ${errors.client_last_name ? 'border-red-500' : ''}`}
							value={formData.client_last_name}
							onChange={(e) => {
								setFormData((prev) => ({ ...prev, client_last_name: e.target.value }));
								if (errors.client_last_name) setErrors((prev) => ({ ...prev, client_last_name: '' }));
							}}
						/>
						{errors.client_last_name && <p className="mt-1 text-sm text-red-500">{errors.client_last_name}</p>}
					</div>
				</div>

				<div>
					<label className="mb-1 block text-sm font-medium text-gray-700">{t("Email")}</label>
					<input
						type="email"
						required
						className={`w-full rounded-md border px-3 py-2 ${errors.client_email ? 'border-red-500' : ''}`}
						value={formData.client_email}
						onChange={(e) => {
							setFormData((prev) => ({ ...prev, client_email: e.target.value }));
							if (errors.client_email) setErrors((prev) => ({ ...prev, client_email: '' }));
						}}
					/>
					{errors.client_email && <p className="mt-1 text-sm text-red-500">{errors.client_email}</p>}
				</div>

				{/* 图片上传部分 */}
				<div>
					<label className="mb-1 block text-sm font-medium text-gray-700">
						{t("Upload Images")} ({t("Optional")})
					</label>
					<Upload
						listType="picture-card"
						fileList={fileList}
						onChange={handleUploadChange}
						beforeUpload={beforeUpload}
						maxCount={6}
						multiple
						accept="image/*"
						customRequest={({ onSuccess }) => {
							if (onSuccess) {
								onSuccess("ok");
							}
						}}
					>
						{fileList.length >= 6 ? null : uploadButton}
					</Upload>
				</div>

				<button
					type="submit"
					disabled={submitting}
					className="w-full !rounded-sm bg-main px-4 py-2 text-white  disabled:bg-blue-300"
				>
					{submitting ? t("Submitting") : t("Submit Review")}
				</button>
			</form>
		</div>
	);
}

function ReviewCard({ review }: { review: Review }) {
	return (
		<div className="rounded-lg border p-4 shadow-sm transition-shadow hover:shadow-md">
			<div className="mb-2 flex items-center justify-between">
				<div>
					<h3 className="text-lg font-semibold">{review.comment_title}</h3>
					<p className="text-sm text-gray-600">
						{review.client_first_name} {review.client_last_name}
					</p>
				</div>
				<div className="flex items-center">
					{[...Array(5)].map((_, i) => (
						<i key={i} className={`ri-star-${i < review.rating ? "fill" : "line"} text-yellow-400`}></i>
					))}
				</div>
			</div>
			<p className="mb-4 text-gray-700">{review.comment_content}</p>

			{/* 评论图片展示 */}
			{review.image_urls && review.image_urls.length > 0 && (
				<div className="mb-4 grid grid-cols-6 gap-2 max-md:grid-cols-2 max-lg:grid-cols-3">
					{review.image_urls.map((image, index) => (
						<div key={index} className="group relative aspect-square">
							<img
								src={image}
								alt={`Review image ${index + 1}`}
								className="h-[100px] w-[100px] cursor-pointer rounded-lg object-cover"
							/>
						</div>
					))}
				</div>
			)}

			{review.created_at && (
				<p className="text-sm text-gray-500">{new Date(review.created_at).toLocaleDateString()}</p>
			)}
		</div>
	);
}

function ReviewList({ reviews }: { reviews: Review[] }) {
	const t = useTranslations("Review");

	return (
		<div className="space-y-4">
			<div className="mb-6 flex items-center justify-between">
				<h2 className="text-xl font-semibold">{t("Product Reviews")}</h2>
				<div className="text-sm text-gray-600">
					{reviews.length} {t("reviews")}
				</div>
			</div>


			{/* 评论列表 */}
			<div className="reviews space-y-6 h-[700px] overflow-y-auto ">
				{reviews.map((review, index) => (
					<ReviewCard key={index} review={review} />
				))}
			</div>
		</div>
	);
}

export default function ProductReviews({ productId }: ProductReviewsProps) {
	const [reviews, setReviews] = useState<Review[]>([]);
	const [loading, setLoading] = useState(true);
	const t = useTranslations("Review");

	useEffect(() => {
		const fetchReviews = async () => {
			try {
				const { data } = await axios.get(
					`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/saleor/product_comment`,
					{
						params: {
							product_id: productId,
							limit: 100,
							page: 1,
						},
					},
				);
				if (data.code == 200) {
					setReviews(data.detail.ret);
				}
			} catch (error) {
				console.error("Error fetching reviews:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchReviews();
	}, [productId]);

	return (
		<div className="grid grid-cols-1 gap-8 py-6 md:grid-cols-2">
			{/* 左侧评论列表或无评论提示 */}
			<div className="space-y-6 ">
				{loading ? (
					<div className="flex justify-center py-8">
						<div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
					</div>
				) : !reviews.length ? (
					<div className="flex flex-col items-center justify-center rounded-lg bg-gray-50 py-12">
						<div className="mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-gray-100">
							<i className="ri-emotion-sad-line text-4xl text-gray-500"></i>
						</div>
						<h2 className="mb-3 text-xl font-semibold text-gray-700">{t("No Reviews Yet")}</h2>
						<p className="max-w-md text-center text-sm text-gray-600">
							{t("Be9988")}
						</p>
					</div>
				) : (
					<ReviewList reviews={reviews} />
				)}
			</div>

			{/* 右侧评论表单 */}
			<ReviewForm productId={productId} />
		</div>
	);
}
