"use client";
import { useCompareStore } from "@/lib/store/Compare.store";
import { Drawer } from "antd";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { fetchData } from "../Collection";
import { RingLoader } from "react-spinners";
import { ProductListItemFragment } from "@/gql/graphql";
import { filterCateImg } from "../Product/product-card";
import {useRouter  } from "@/navigation";
import useIsMobile from "@/lib/hooks/useIsMobile";
function Index() {
	let { show, changeShow } = useCompareStore();

	const [CompareList, setCompareList] = useState([]);
	const t = useTranslations();
	const { compareIds,removeAllCompare,removeCompareProduct } = useCompareStore();
	const [loading, setLoading] = useState(false);
	const locale = useLocale();

  let isMobile=useIsMobile()
  let router= useRouter()
	useEffect(() => {
		// if (show) {
		// 	return;
		// }

		if (!compareIds.length || !locale) return setCompareList([]);
		fetchData(compareIds, setLoading, locale).then((res) => {

  // 按照 compareIds 顺序排序
  const sortedData = compareIds
    .map((id) => res.find((item) => item.node.id === id))
    .filter(Boolean); // 去掉可能未匹配到的数据

  // 更新排序后的结果
  setCompareList(sortedData);
		});
	}, [show,compareIds]);

  function removeId(id){
    console.log(compareIds.length);
    
    removeCompareProduct(id)

    if(compareIds.length==1){
      setTimeout(()=>{
        changeShow(false)
      },500)
     
    }
  }


	return (
		<Drawer
			placement={"bottom"}
			closable={false}
			onClose={() => changeShow(false)}
			open={show}
			height={isMobile?'50%':"25%"}
      // mask={false}
      // rootClassName="!hidden md:!block"
		>
			<div className="box-border flex h-full   w-full flex-col">
				<div className="w-full text-right">
					{" "}
					<i className="ri-close-fill cursor-pointer text-3xl" onClick={() => changeShow(false)}></i>
				</div>
				<div className="flex flex-1 max-md:flex-col">
					<div className="max-md:hidden max-lg:!text-xl box-border flex  cursor-pointer items-center justify-center  gap-x-2 bg-white px-3 py-2 text-4xl  group-hover:bg-black">
						{t('nav.Compare1')} <span className="max-lg:hidden">{t('common.Products')}</span>
					</div>
					<div className="h-full overflow-hidden w-full flex-1 box-border px-5 flex items-center gap-x-5 max-md:mb-4 max-md:overflow-x-auto max-md:flex-row max-md:w-full max-md:pb-4 max-md:snap-x max-md:snap-mandatory [&::-webkit-scrollbar]:h-1.5 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300">
          { CompareList.map((item) => (
                <CompareItem key={item.node.id} delFn={removeId}  productItem={item.node as ProductListItemFragment} />
              ))}
					</div>
					<div className="flex flex-col  items-center justify-center max-md:flex-row max-md:justify-between">
						<div onClick={()=>{
                changeShow(false)
                router.push('/compare')
                
              
            }} className="mb-4 box-border flex cursor-pointer items-center justify-center gap-x-2  rounded-sm bg-black px-5 py-2 text-3xl text-[18px] text-white">
							{t('nav.Compare1')}
						</div>
						<span className="underline cursor-pointer" onClick={()=>{
              removeAllCompare()
              changeShow(false)
            }}>	{t("common.2404e3fb11a4cd4619b87d75ba56e345d8b0")}</span>
					</div>
				</div>
			</div>
		</Drawer>
	);
}
function CompareItem({ productItem ,delFn}: { productItem: ProductListItemFragment,delFn:Function }) {
	return (
		<div className="relative flex-shrink-0 max-md:snap-center">
				<img
					src={filterCateImg(productItem.metadata)[0]?.url || "/image/default-image.webp"}
					alt="longshengmfg-logo"
					className="object-cover rounded-sm w-[141px] h-[150px]"
				></img>
        <div onClick={()=>delFn(productItem.id)} className="absolute top-0 right-0 rounded-full w-6 h-6 cursor-pointer bg-white flex flex-col items-center justify-center">
        <i className="ri-close-line"></i>
        </div>
		</div>
	);
}

export default React.memo(Index);
