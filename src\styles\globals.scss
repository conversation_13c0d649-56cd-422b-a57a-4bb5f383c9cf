@tailwind base;
@tailwind components;
@tailwind utilities;

/* Main color */
:root {
  --black: #000000;
	--green: #d2ef9a;
	--secondary: #696c70;
	--secondary2: #a0a0a0;
	--white: #ffffff;
	--surface: #f7f7f7;
	--red: #db4444;
	--purple: #8684d4;
	--success: #3dab25;
	--yellow: #ecb018;
	--pink: #f4407d;
	--line: #e9e9e9;
	--outline: rgba(0, 0, 0, 0.15);
	--surface1: rgba(255, 255, 255, 0.1);
	--surface2: rgba(255, 255, 255, 0.2);
}
/* Reset */
* {
	padding: 0;
	margin: 0;
	box-sizing: border-box;
}
// a {
// 	color: #1f1f1f !important;
// }

html,
body {
	font-size: 16px;
	line-height: 26px;
	color: var(--black);
	font-weight: 400;
	overflow-x: hidden;
	user-select: none;
	scroll-behavior: smooth;
	font-family: Aria<PERSON>, serif;
}

//div,
//span,
//p {
//	font-size: 16px;
//	line-height: 26px;
//}

select {
	appearance: none;
	outline: none;
}

.select-block {
	position: relative;

	> .arrow-down {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 16px;
	}
}

.search input,
textarea {
	outline: none;
	border: 1px solid transparent;
	transition: border-color ease 0.3s;
}

.search input:focus,
textarea:focus {
	border-color: var(--black);
}
 input[type='text'] {
  outline: none !important;
  box-shadow: none !important;
}
input[type='text']:focus {
  outline: none !important;
  box-shadow: none !important;
}
textarea:focus {
  outline: none !important;
  box-shadow: none !important;
}

input[type='tel']:focus{
  box-shadow: none !important;
}
.react-tel-input .form-control {
  border-radius: 1px !important;
}
 input:focus{
  border: 1px solid #000 !important;
  box-shadow: none !important;
}
input.form-control{
  border-radius: 1px !important;
  color: #000 !important;


}

.Tourist0rderInquiryPage  input:focus{
  border: none !important;
  box-shadow: none !important;
}
.react-tel-input .flag-dropdown{
  border: none !important;
  border-right:1px solid #d1d5db !important;
  background-color: transparent !important;
}
.react-tel-input .flag-dropdown:hover{
  background-color: transparent !important;
}

/* 注册表单手机号输入框样式 */
.signup-phone-input .country {
  text-align: left !important;
  padding-left: 30px !important;
}

.selected-flag {
  background: transparent !important;
} 
.selected-flag:hover {
  background: transparent !important;
}
select:focus{
  box-shadow: none !important;
}
.c-flex {
  @apply flex justify-center items-center;
}

.s-flex {
  @apply flex justify-start items-center;
}

.e-flex {
  @apply flex justify-end items-center;
}

.b-flex {
  @apply flex justify-between items-center;
}
/* Main class used in Anvogue */
.container {
  @apply mx-auto max-w-main;
	width: 100%;

}
.container1730 {
  @apply mx-auto px-2 max-md:mx-0;
  max-width: 1730px;

}
.containernone {
  @apply mx-auto max-w-main;
	width: 100%;

}
.animate__delay-10 {
  animation-delay: 10ms;
}

.animate__delay-20 {
  animation-delay: 20ms;
}

.animate__delay-30 {
  animation-delay: 30ms;
}

.animate__delay-40 {
  animation-delay: 40ms;
}

.animate__delay-50 {
  animation-delay: 50ms;
}

.animate__delay-60 {
  animation-delay: 60ms;
}

.animate__delay-70 {
  animation-delay: 70ms;
}

.animate__delay-80 {
  animation-delay: 80ms;
}

.animate__delay-90 {
  animation-delay: 90ms;
}

.animate__delay-100 {
  animation-delay: 100ms;
}

.animate__delay-200 {
  animation-delay: 200ms;
}

.animate__delay-300 {
  animation-delay: 300ms;
}

.animate__delay-400 {
  animation-delay: 400ms;
}

.animate__delay-500 {
  animation-delay: 500ms;
}

.animate__delay-600 {
  animation-delay: 600ms;
}

.animate__delay-700 {
  animation-delay: 700ms;
}

.animate__delay-800 {
  animation-delay: 800ms;
}

.animate__delay-900 {
  animation-delay: 900ms;
}

.animate__delay-1000 {
  animation-delay: 1000ms;
}

.animate__delay-1100 {
  animation-delay: 1100ms;
}

.animate__delay-1200 {
  animation-delay: 1200ms;
}

.animate__delay-1300 {
  animation-delay: 1300ms;
}

.animate__delay-1400 {
  animation-delay: 1400ms;
}

.animate__delay-1500 {
  animation-delay: 1500ms;
}

.animate__delay-1600 {
  animation-delay: 1600ms;
}

.animate__delay-1700 {
  animation-delay: 1700ms;
}

.animate__delay-1800 {
  animation-delay: 1800ms;
}

.animate__delay-1900 {
  animation-delay: 1900ms;
}

.animate__delay-2000 {
  animation-delay: 2000ms;
}

/*美化所有滚动条*/
::-webkit-scrollbar {
  @apply w-[10px];
}

::-webkit-scrollbar-thumb {
  @apply rounded-full bg-[#dbdbdb];
}

.home-banner-bullet {
  @apply w-[12px] h-[12px] bg-white;
}

.home-banner:hover .swiper-button-next,
.home-banner:hover .swiper-button-prev {
  @apply opacity-0;
}

.home-banner .swiper-button-next,
.home-banner .swiper-button-prev {
  @apply w-[40px] h-[40px] rounded-full bg-white max-md:hidden duration-300 opacity-0;
}

.home-banner .swiper-button-next::after,
.home-banner .swiper-button-prev::after {
  @apply text-main max-md:hidden text-lg;
}

.home-banner .swiper-button-prev:hover,
.home-banner .swiper-button-next:hover {
  @apply bg-white;
}

.home-banner .swiper-button-prev:hover::after,
.home-banner .swiper-button-next:hover::after {
  @apply text-white;
}
.home-banner 
.swiper-pagination-bullets.swiper-pagination-horizontal {
    top: 50% !important;
    right: 10px !important;
    left: auto;
    transform: translateY(-50%) !important;
    width: auto !important;
    height: auto !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 10px; /* 调整分页指示器之间的间距 */
}
.home-banner .swiper-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active::before {
  border-color: #fff !important;
}
.home-banner  .swiper-pagination-bullet-active,.home-banner .swiper-pagination-bullet{
  background: #fff !important;
  border-color: #fff !important;

}


.Header .swiper-button-next,
.Header .swiper-button-prev {
  @apply opacity-0;
}

.Header .swiper-button-next,
.Header .swiper-button-prev {
  @apply w-[40px] h-[40px] rounded-full  max-md:hidden duration-300 opacity-0;
}
.Header  .swiper-button-next.swiper-button-disabled,
.Header  .swiper-button-prev.swiper-button-disabled{
  @apply opacity-0;
}


.Header .swiper-button-next::after,
.Header .swiper-button-prev::after {
  @apply text-white max-md:hidden text-lg;
}

.Header .swiper-button-prev:hover,
.Header .swiper-button-next:hover {
  @apply bg-transparent;
}

.Header .swiper-button-prev:hover::after,
.Header .swiper-button-next:hover::after {
  @apply text-white;
}



.search input[type='text'] {
  border: 1px solid #F8F8F8 !important;
  outline: none !important;
  box-shadow: none !important;
}
.search input[type='text']:focus {
  border: 1px solid #F8F8F8 !important;
  outline: none !important;
  box-shadow: none !important;
}
.text-sub-display {
	font-size: 18px;
	font-weight: 600;
	line-height: 24px;
	letter-spacing: 1.8px;
	text-transform: uppercase;
}

.text-display {
	font-size: 80px;
	font-weight: 500;
	line-height: 88px;
	text-transform: capitalize;
}

.heading1 {
	font-size: 56px;
	line-height: 68px;
	font-weight: 500;
	text-transform: capitalize;
}

.heading2 {
	font-size: 44px;
	line-height: 50px;
	font-weight: 600;
	text-transform: capitalize;
}

.heading3 {
	font-size: 36px;
	line-height: 40px;
	font-weight: 600;
	text-transform: capitalize;
}

.heading4 {
	font-size: 30px;
	line-height: 42px;
	font-weight: 600;
	text-transform: capitalize;
}

.heading5 {
	font-size: 24px;
	line-height: 30px;
	font-weight: 600;
	text-transform: capitalize;
}

.heading6 {
	font-size: 20px;
	line-height: 28px;
	font-weight: 600;
	text-transform: capitalize;
}

.text-title {
	font-size: 16px;
	line-height: 24px;
	font-weight: 500;
	text-transform: capitalize;
}

.body1 {
	font-size: 18px;
	line-height: 28px;
	font-weight: 400;
}

.text-button {
	font-size: 16px;
	line-height: 26px;
	font-weight: 600;
	text-transform: capitalize;
}

.text-button-uppercase {
	font-size: 14px;
	line-height: 20px;
	font-weight: 600;
	text-transform: uppercase;
}

.caption1 {
	font-size: 14px;
	line-height: 22px;
	font-weight: 400;
}

.caption2 {
	font-size: 12px;
	line-height: 16px;
	font-weight: 400;
}

.button-main {
	font-size: 14px;
	line-height: 20px;
	font-weight: 600;
	text-transform: uppercase;
	color: var(--white);
	background-color: var(--black);
	padding: 16px 40px;
	border-radius: 12px;
	display: inline-block;
	cursor: pointer;
	transition: all ease 0.4s;

	&:hover {
		background-color: var(--green);
		color: var(--black);

		svg {
			path {
				fill: var(--black);
			}
		}
	}

	&.bg-white {
		&:hover {
			background-color: var(--black);
			color: var(--white);
		}
	}
}

@media (max-width: 1023.99px) {
	.text-sub-display {
		font-size: 16px;
		line-height: 24px;
		letter-spacing: 1.1px;
	}

	.text-display {
		font-size: 42px;
		line-height: 50px;
	}

	.heading1 {
		font-size: 36px;
		line-height: 48px;
	}

	.heading2 {
		font-size: 32px;
		line-height: 40px;
	}

	.heading3 {
		font-size: 30px;
		line-height: 38px;
	}

	.heading4 {
		font-size: 26px;
		line-height: 32px;
	}

	.heading5 {
		font-size: 22px;
		line-height: 28px;
	}

	.heading6 {
		font-size: 18px;
		line-height: 26px;
	}

	.button-main {
		padding: 12px 24px;
		border-radius: 10px;
	}
}

@media (max-width: 767.98px) {
	html,
	body,
	div,
	span,
	p {
		font-size: 14px;
		line-height: 24px;
	}

	.text-sub-display {
		font-size: 12px;
		line-height: 16px;
		letter-spacing: 1.1px;
	}

	.text-display {
		font-size: 24px;
		line-height: 30px;
	}

	.heading1 {
		font-size: 24px;
		line-height: 32px;
	}

	.heading2 {
		font-size: 22px;
		line-height: 30px;
	}

	.heading3 {
		font-size: 20px;
		line-height: 28px;
	}

	.heading4 {
		font-size: 18px;
		line-height: 28px;
	}

	.heading5 {
		font-size: 16px;
		line-height: 26px;
	}

	.heading6 {
		font-size: 16px;
		line-height: 24px;
	}

	.text-title {
		font-size: 14px;
		line-height: 20px;
	}

	.body1 {
		font-size: 16px;
		line-height: 26px;
	}

	.text-button {
		font-size: 14px;
		line-height: 24px;
	}

	.text-button-uppercase {
		font-size: 12px;
		line-height: 16px;
	}

	.caption1 {
		font-size: 13px;
		line-height: 20px;
	}

	.button-main {
		font-size: 12px;
		line-height: 16px;
		padding: 10px 16px;
		border-radius: 8px;
	}
}
.ant-collapse-header-text {
	font-weight: bold;
	font-size: 16px;
}
.ant-collapse-content-box {
	color: #717171;
	font-size: 14px;
}
.ant-tabs-tab-btn {
	font-weight: bold;
}
.ant-form-item-label {
	font-weight: bold;
}

.ant-tooltip-inner {
	border-radius: 6px !important;
}

.adSwiper{
	.swiper-pagination-bullet{
		border-color: white !important;
		background: white !important;
	}
	.swiper-pagination-bullet-active{
		background: #0e6aed !important;
		border-color: #0e6aed !important;
	}
}

.footer .ant-btn {
  background: #fd0100 !important;
}

.footer .ant-input {
  background: #2c2c2c !important;
  color: #e9e9e9 !important;
  border: 1px solid #2c2c2c !important;
  color: #fff  !important;
}
.footer .ant-input:focus {

  border:none !important;
}

.footer input {
  box-shadow: none !important;

  border:none !important;
}




.myhover {
  position: relative;
  overflow: hidden;
}

.myhover::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 100%;
  width: 300%;
  height: 300%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.5), transparent);
  transition: transform 0.5s ease;
  transform: translate(-50%, -50%) scale(0);
  pointer-events: none;
}

.myhover:hover::before {
  transform: translate(-50%, -50%) scale(1);
}
:where(.css-dev-only-do-not-override-1hyj81g) a:hover {
  color: #f39700 !important;
}

.textborder{
  @apply  font-semibold text-[60px] text-[rgba(0,0,0,0)] leading-[96px] text-left not-italic normal-case;
  text-stroke: 2px #f39700;
  -webkit-text-stroke: 2px #f39700;
  position: absolute;
  left: 0;
  top: -35px;
  z-index: -1;
}

.textborder1{
  @apply  font-semibold text-[60px] text-[rgba(0,0,0,0)] leading-[96px] text-left not-italic normal-case;
  text-stroke: 2px #f39700;
  -webkit-text-stroke: 2px #f39700;
  position: absolute;
  left: 0;
  top: -35px;
  z-index: 8;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="number"] {
    -moz-appearance: textfield;
}

.ant-breadcrumb>ol{
  display: flex !important;
}

.banner .swiper-wrapper {
  height: auto !important;;
}

.ant-btn{
  @apply bg-main text-white;
}

/*侧边栏*/
/* Sidebar.css */
.sidebar-container {
  position: fixed;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  z-index: 999;
  transition: transform 0.3s ease;
}

.sidebar-container.closed {
  transform: translateX(100%);
}

.sidebar {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  transition: transform 0.3s ease;
}

.sidebar-item {
  // width: 60px;
  // height: 52px;
background-color: rgba(102, 102, 102, 0.07);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: width 0.3s ease;
}

.mixBlendmode{
  mix-blend-mode: difference !important;
}

.iconfont.map {
  width: 24px;
  height: 24px;
  background: url('/image/map.gif') #000 no-repeat 50%;
  background-size: cover;
  display: block;
  border-radius: 50%;
  top: -1px;
  position: relative;
}



// Airwallex
.field-container {
  margin: 0 auto 16px;
  width: 300px;
}

div.field-label {
  text-align: left;
  border: 0;
}

p#error {
  background: #ffebee;
  border-radius: 5px;
  padding: 5px 10px;
  max-width: 400px;
  margin: auto;
}

div.instruction-container {
  padding: 10px 50px;
  text-align: left;
}

span#code {
  font-family: monospace;
  background-color: lightgrey;
  font-size: 16px;
  margin: 0px 5px;
}

p#bullet {
  font-weight: bold;
}

.snr-2 {
  position: relative;
  padding: 10vh 0;
  overflow: hidden;
}
.snr-2-dd {
  background: #0a2349;
  overflow: hidden;
  position: absolute;
  top: 0;
  max-height: 50vh;
}

.ys_gd {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  right: 10%;
  width: 460px;
  background: rgba(0, 0, 0, 0.4);
  padding: 50px 50px 20px;
}

.snr-3-w {
  background: linear-gradient(#fff, #f4f4f4);
  padding-bottom: 6vh;
}

 .ys_gd .swiper-pagination-bullet{

  background-color: #fff !important;
opacity: 0.2 !important;
}

.ys_gd .swiper-pagination-bullet-active{

  background-color: #fff !important;
  opacity: 1 !important;
}
.ys_gd  .swiper-pagination-bullet-active::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 8px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border: 1px solid #fff !important;
  transition: all ease 0.5s;
  border-radius: 50%;
}
.srrgd {
  padding-top: 6vh  !important;
  padding-bottom: 40px  !important;
}
@keyframes moveTopLeft {
  0% { transform: translate(0, 0); }
  50% { transform: translate(40%, 40%); }
  100% { transform: translate(0, 0); }
}

@keyframes moveTopRight {
  0% { transform: translate(0, 0); }
  50% { transform: translate(-40%, 40%); }
  100% { transform: translate(0, 0); }
}

@keyframes moveBottomLeft {
  0% { transform: translate(0, 0); }
  50% { transform: translate(40%, -40%); }
  100% { transform: translate(0, 0); }
}

@keyframes moveBottomRight {
  0% { transform: translate(0, 0); }
  50% { transform: translate(-40%, -40%); }
  100% { transform: translate(0, 0); }
}

.animate-moveTopLeft {
  animation: moveTopLeft 3s ease-in-out infinite;
}

.animate-moveTopRight {
  animation: moveTopRight 3s ease-in-out infinite;
}

.animate-moveBottomLeft {
  animation: moveBottomLeft 3s ease-in-out infinite;
}

.animate-moveBottomRight {
  animation: moveBottomRight 3s ease-in-out infinite;
}
.my-masonry-grid {
  display: flex;
  margin-left: -16px; /* 负的间距大小 */
  width: auto;
}

.my-masonry-grid_column {
  padding-left: 16px; /* 间距大小 */
  background-clip: padding-box;
}

.my-masonry-grid_column > div { /* 这里的 div 是指放在 <Masonry> 中的元素 */
  margin-bottom: 16px;
}

.w1520 {
  @apply mx-auto w-[85%] max-lg:w-[88%];

}

.boxShadow{
  box-shadow: 0 8px 24px #959da533;
}

.Information .ant-popconfirm .ant-popconfirm-buttons {
  display: none !important;
}

:where(.css-dev-only-do-not-override-ac88t2).ant-popconfirm .ant-popconfirm-buttons {
  display: none !important;
}

.ProductDescription table{
  width: 100%;
  border: 1px solid #ebebeb;
  border-collapse: collapse; /* 合并边框 */
}

.ProductDescription td{
  box-sizing: border-box;
  padding: 10px;
  border: 1px solid #ebebeb;
}

/* RotatingCube.css */


/* 调整立方体容器宽高 */
.cube-container-small {
  position: relative;
  width: 20px; /* 容器宽高 */
  height: 20px;
  perspective: 200px; /* 调整透视深度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 调整立方体大小 */
.cube-small {
  position: relative;
  width: 20px; /* 立方体宽高 */
  height: 20px;
  transform-style: preserve-3d;
  animation: rotateCubeSmall 3s infinite linear; /* 设置旋转动画 */
}

/* 调整每个面的样式 */
.cube-face {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 6px; /* 缩小字体大小 */
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid white; /* 缩小边框 */
}

/* 定义立方体每个面的位置 */
.cube-face-front {
  transform: translateZ(10px); /* 深度为立方体一半 */
}
.cube-face-back {
  transform: rotateY(180deg) translateZ(10px);
}
.cube-face-right {
  transform: rotateY(90deg) translateZ(10px);
}
.cube-face-left {
  transform: rotateY(-90deg) translateZ(10px);
}
.cube-face-top {
  transform: rotateX(90deg) translateZ(10px);
}
.cube-face-bottom {
  transform: rotateX(-90deg) translateZ(10px);
}

/* 动画 */
@keyframes rotateCubeSmall {
  0% {
    transform: rotateX(0deg) rotateY(0deg);
  }
  100% {
    transform: rotateX(360deg) rotateY(360deg);
  }
}

.user .ant-popover-inner{
  border-radius: 5px !important;
}

.ai input:focus{
  border:none !important;
}

/* 在您的 CSS 文件中添加以下样式 */
.reviews {
  /* 隐藏滚动条 */
  overflow-y: scroll; /* 仍然允许滚动 */
  scrollbar-width: none; /* Firefox */
}

.reviews::-webkit-scrollbar {
  display: none; /* Chrome, Safari 和 Opera */
}

.reviews textarea:focus{
  border-color:#000 !important;
}

.OrderList .ant-card-body{
  padding: 0 !important;

}

.product-description {
  height: 44px; /* 两行文字的高度 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.banner .swiper-wrapper{
  height: 100% !important;
}

.banner .swiper-slide{
    height: 100% !important;
}
.top-nav-coll .ant-collapse-content-box {
 padding-top: 0 !important;
 padding-bottom: 0 !important;
}