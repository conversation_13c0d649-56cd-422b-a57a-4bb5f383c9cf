'use client'
import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { HomeTaile } from "@/components/Contact/ConcatPage";
import type { CollapseProps } from 'antd';
import { Collapse } from 'antd'
const FaqList = () => {
	const t = useTranslations()
	const faqKeys = Array.from({ length: 25 }, (_, i) => `q${i + 1}`);

	const items: CollapseProps['items'] = faqKeys.map((key, index) => {
		const questionKey = key;
		const answerKey = `a${index + 1}`;
		// 检查翻译是否存在，避免出现错误
		try {
			// const question = tfaq(questionKey);
			// const answer = tfaq(answerKey);

			return {
				key: String(index + 1),
				label:  `问题${index + 1}`,
				children: <p style={{ whiteSpace: 'pre-line', color: "#000" }}>答案{index + 1}</p>,
			};
		} catch (error) {
			// 如果找不到翻译，则跳过此项
			return null;
		}
	}).filter(Boolean); // 过滤掉null值

	return <>
		<HomeTaile msg={t("menu.faqs")} />
		<section className="py-20">
			<div className="container">
				<Collapse accordion items={items} />
			</div>
			<style jsx global>{`
                .ant-collapse {
					background: #fff
				}
            `}</style>

		</section>
	</>
}
export default FaqList
