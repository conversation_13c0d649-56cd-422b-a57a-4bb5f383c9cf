'use client'
import * as Icon from "@phosphor-icons/react/dist/ssr";
import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { Link } from "@/navigation";

const BlogSearch=()=>{
	const t=useTranslations()
	const [searchTitle,setSearchTitle]=useState("")


	return <>
		<div className='form-search relative w-full h-12' >
			<input className='py-2 px-4 w-full h-full border border-line rounded-lg' type="text"   value={searchTitle}
						 placeholder={t('base.Search')} onChange={e=>	setSearchTitle(e.target.value)} />
			<Link href={searchTitle ? `/blog?search=${encodeURIComponent(searchTitle)}` : '/blog'}>
				<Icon.MagnifyingGlass
					className='heading6 text-secondary hover:text-black duration-300 absolute top-1/2 -translate-y-1/2 right-4 cursor-pointer' />
			</Link>
		</div>
	</>
}
export default   React.memo(BlogSearch)
