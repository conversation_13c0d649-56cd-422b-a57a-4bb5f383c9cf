"use client";
import React from "react";
import { Button } from "@/components/Button";
import { SubmitHandler, useForm } from "react-hook-form";
import { RingLoader } from "react-spinners";
import { getCookie, setCookie } from "cookies-next";
import { useTranslations } from "next-intl";
import { App } from "antd";

type FormValues = {
	email?: string;
	remember?: boolean;
	onSubmit: (data?: React.BaseSyntheticEvent<object, any, any> | undefined) => Promise<void>;
	setLoginModalOn?: any;
};

interface LoginFormProps {
	setLoginModalOn?: any;
}

const LoginForm = ({ setLoginModalOn }: LoginFormProps) => {
	const t = useTranslations();
	let get_form_info: any = getCookie("created__user__info");
	if (get_form_info) {
		get_form_info = JSON.parse(get_form_info);
	}
	const {
		register,
		handleSubmit,
		formState: { errors },
		reset,
	} = useForm<FormValues>();
	const { message } = App.useApp();
	const [loading, setLoading] = React.useState(false);

	const onSubmit: SubmitHandler<FormValues> = async (data: any) => {
		// console.log(data,'data');
		setLoading(true);

		try {
			// 调用API发送重置密码邮件
			const response = await fetch(process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL + "/saleor/account_pwd_email", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					account_email: data.email,
				}),
			});

			if (response.ok) {
				message.success(t("common.Mailsuccess"));
			} else {
				message.error(t("common.Emailerror"));
			}
		} catch (error) {
			message.error(t("common.Emailfailed"));
		} finally {
			setLoading(false);
		}
	};
	return (
		<div className="w-full">
			<div className="mt-1 md:mt-4 user">
				<form onSubmit={handleSubmit(onSubmit)} className="space-y-2 md:space-y-4">
					{/* 邮箱字段 */}
					<div>
						<label htmlFor="email" className="block text-left text-xs md:text-sm font-medium text-gray-700 mb-1 md:mb-1.5">
							{t("common.Email")} <span className="text-red-500">*</span>
						</label>
						<input
							type="email"
							id="email"
							className={`w-full px-2 py-1.5 md:px-3 md:py-2 text-xs md:text-sm placeholder:text-xs md:placeholder:text-sm placeholder:text-gray-300
								border rounded-sm outline-none bg-white text-black transition-colors duration-200
								focus:ring-1 md:focus:ring-2 focus:ring-themePrimary500 focus:border-transparent
								${errors.email ? "border-red-500" : "border-themeSecondary300"}`}
							defaultValue={get_form_info?.email}
							placeholder={t("form.109a2790cc6b48461908f883b7b41549f9b3")}
							{...register("email", {
								required: t("common.Email_Required"),
								pattern: {
									value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
									message: t("common.Email_Invalid")
								}
							})}
						/>
						{errors.email && (
							<p className="mt-0.5 text-xs text-red-500">{errors.email.message}</p>
						)}
					</div>

					{/* 提交按钮 */}
					<div className="pt-1 md:pt-3">
						<Button
							disabled={loading}
							className={`flex gap-2 md:gap-3 items-center justify-center w-full py-2 md:py-3 text-xs md:text-sm font-medium
								bg-black text-white rounded-sm transition-all duration-200 hover:bg-gray-800
								disabled:opacity-50 disabled:cursor-not-allowed
								${loading ? "bg-themeSecondary800" : ""}`}
						>
							{loading && <RingLoader color="#fff" size={14} />}
							{loading ? t("message.7f9e518a7a1d1e4bc3e8990bed1d9be4d404") + "..." : t("common.Reset_password")}
						</Button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default LoginForm;
