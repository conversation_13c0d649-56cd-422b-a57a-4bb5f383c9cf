"use client";

import { useProductStore } from "@/lib/store/product.store";


type Props = {
  price: string | number;
  className?: string;
  size?: "xs" | "sm" | "md" | "lg" | "xl";
}

export default function Price(props: Props) {
  const { price, className = "", size = "md" } = props;
  const { currencyUnit } = useProductStore();
  if (+price <= 0) return null;

  // 确保 price 是数字并保留两位小数
  const formattedPrice = typeof price === 'number' ? price.toFixed(2) : parseFloat(price).toFixed(2);
  
  const pce = formattedPrice.startsWith(currencyUnit) ? formattedPrice : `${currencyUnit}  ${formattedPrice}`;
  
  return <span className={`${className} text-${size} py-2`}>
        {pce}
    </span>;
}