"use client";
import { TLocale } from "@/lib/@types/locale";
import { SITE_LOCALES } from "@/lib/constant";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { usePathname } from "next/navigation";
import { useLocale } from "next-intl";
import { defaultLocale } from "@/config";
import { Link } from "@/navigation";
import FlagIcon from "@/components/Flag";
import { Drawer } from "antd";

const Translate = ({ lastScrollPosition }: { lastScrollPosition?: number }) => {
	const currentLocales: TLocale[] = SITE_LOCALES;
	const [isOpenLanguage, setIsOpenLanguage] = useState(false);
	const pathname = usePathname();
	const locale = useLocale();
	const [currentSlug, setCurrentSlug] = useState("");
	const dropdownRef = useRef<HTMLDivElement>(null);

	const getCurrentLocale = useCallback(() => {
		return currentLocales.find((item) => item.code === locale);
	}, [currentLocales, locale]);

	const toggleLanguageMenu = useCallback(() => {
		setIsOpenLanguage((prev) => !prev);
	}, []);

	const closeLanguageMenu = useCallback(() => {
		setIsOpenLanguage(false);
	}, []);

	useEffect(() => {
		if (pathname) {
			setCurrentSlug(locale === defaultLocale ? pathname : pathname.replace(locale, ""));
		}
	}, [pathname, locale]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (dropdownRef.current?.contains(event.target as Node)) {
				return;
			}

			closeLanguageMenu();
		};

		document.addEventListener("click", handleClickOutside);

		return () => {
			document.removeEventListener("click", handleClickOutside);
		};
	}, [isOpenLanguage, closeLanguageMenu]);

	if (process.env.NEXT_PUBLIC_IS_I18N !== "true") {
		return null;
	}
	return (
    <div className="w-full h-full !text-right">
          <div ref={dropdownRef} className="relative z-50 inline-block  ">
    <button
      onClick={toggleLanguageMenu}
      className={`flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium  transition-colors `}
      aria-expanded={isOpenLanguage}
      aria-haspopup="true"
    >
      <FlagIcon flag={getCurrentLocale()?.flag || ""} />
      <span>{getCurrentLocale()?.nativeName}</span>
      <svg
        className={`h-4 w-4 transition-transform ${isOpenLanguage ? "rotate-180" : ""}`}
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
        aria-hidden="true"
      >
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <Drawer
        title=""
        placement={'bottom'}
        closable={false}
        onClose={()=>setIsOpenLanguage(false)}
        open={isOpenLanguage}
      >
          {currentLocales.map((item) => (
            <Link
              key={item.code}
              href={`${currentSlug}?lang-switch=1`}
              locale={item.code}
              className="flex items-center gap-2 whitespace-nowrap px-4 py-2 text-sm text-gray-600 transition-colors hover:bg-gray-50 hover:text-gray-900"
              onClick={closeLanguageMenu}
              role="menuitem"
            >
              <FlagIcon flag={item.flag} />
              <span>{item.nativeName}</span>
              {locale === item.code && (
                <svg
                  className="ml-auto h-4 w-4 text-blue-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </Link>
          ))}
      </Drawer>
  </div>
    </div>


	);
};

export default Translate;
