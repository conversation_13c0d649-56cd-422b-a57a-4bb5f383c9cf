'use client'
import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { HomeTaile } from "@/components/Contact/ConcatPage";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import { motion } from "framer-motion";
import { CountUpNumber } from "@/components/CountUpNumber/page";

// 动画变体
const fadeInUp = {
  hidden: { opacity: 0, y: 40, scale: 0.95 },
  visible: {
    opacity: 1, y: 0, scale: 1,
    transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }
  }
};

const fadeInLeft = {
  hidden: { opacity: 0, x: -40, scale: 0.95 },
  visible: {
    opacity: 1, x: 0, scale: 1,
    transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }
  }
};

const fadeInRight = {
  hidden: { opacity: 0, x: 40, scale: 0.95 },
  visible: {
    opacity: 1, x: 0, scale: 1,
    transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.15, delayChildren: 0.1 }
  }
};

const scaleIn = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1, scale: 1,
    transition: { duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }
  }
};

const PatentCertificationPage = () => {
	const t = useTranslations()

	// 专利证书图片数据
	const patentImages = [
		"/image/patents/patent-1.jpg",
		"/image/patents/patent-2.jpg",
		"/image/patents/patent-3.jpg",
		"/image/patents/patent-4.jpg",
		"/image/patents/patent-5.jpg",
		"/image/patents/patent-6.jpg"
	];

	// 认证证书图片数据
	const certificationImages = [
		"/image/certifications/iso9001.jpg",
		"/image/certifications/iso14001.jpg",
		"/image/certifications/high-tech.jpg",
		"/image/certifications/software.jpg",
		"/image/certifications/quality.jpg",
		"/image/certifications/safety.jpg"
	];

	// 专利统计数据
	const patentStats = [
		{ number: "25", label: t("patent.inventionPatents"), color: "text-blue-600" },
		{ number: "18", label: t("patent.utilityModels"), color: "text-green-600" },
		{ number: "12", label: t("patent.designPatents"), color: "text-purple-600" },
		{ number: "8", label: t("patent.softwareCopyrights"), color: "text-orange-600" }
	];

	// 专利详细信息
	const patentDetails = [
		{
			title: "智能制造系统专利",
			number: "ZL202110123456.7",
			type: t("patent.inventionPatents"),
			date: "2021-03-15",
			description: "一种基于人工智能的智能制造系统，能够实现生产过程的自动化控制和优化"
		},
		{
			title: "自动化控制装置",
			number: "ZL202010987654.3",
			type: t("patent.utilityModels"),
			date: "2020-11-20",
			description: "新型自动化控制装置，提高了生产效率和产品质量"
		},
		{
			title: "产品外观设计",
			number: "ZL202030456789.1",
			type: t("patent.designPatents"),
			date: "2020-08-10",
			description: "创新的产品外观设计，兼具美观性和实用性"
		}
	];

	// 认证详细信息
	const certificationDetails = [
		{
			title: "ISO 9001质量管理体系认证",
			issuer: "中国质量认证中心",
			date: "2023-01-15",
			validUntil: "2026-01-14",
			description: "国际标准化组织质量管理体系认证，确保产品和服务质量"
		},
		{
			title: "ISO 14001环境管理体系认证",
			issuer: "SGS认证机构",
			date: "2022-08-20",
			validUntil: "2025-08-19",
			description: "环境管理体系认证，体现企业对环境保护的承诺"
		},
		{
			title: "高新技术企业认证",
			issuer: "科技部",
			date: "2021-12-10",
			validUntil: "2024-12-09",
			description: "国家高新技术企业认定，彰显企业技术创新实力"
		}
	];

	return (
		<div className="min-h-screen bg-white">
			{/* <HomeTaile msg={t("menu.PatentCertification")} /> */}

			{/* 简洁英雄区域 - 苹果风格 */}
			<motion.section
				className="relative py-12 lg:py-20 bg-white"
				initial="hidden"
				animate="visible"
				variants={staggerContainer}
			>
				<div className="container mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center max-w-3xl mx-auto">
						<motion.div
							className="inline-block px-3 py-1 bg-gray-100 rounded-full mb-4"
							variants={scaleIn}
						>
							<span className="text-gray-600 text-xs font-medium uppercase tracking-wide">
								{t("patent.heroSubtitle")}
							</span>
						</motion.div>

						<motion.h1
							className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 tracking-tight"
							variants={fadeInUp}
						>
							{t("patent.heroTitle")}
						</motion.h1>

						<motion.p
							className="text-lg text-gray-600 leading-relaxed mb-8"
							variants={fadeInUp}
						>
							{t("patent.heroDescription")}
						</motion.p>

						{/* 简洁统计数据 */}
						<motion.div
							className="grid grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12"
							variants={staggerContainer}
						>
							{patentStats.map((stat, index) => (
								<motion.div
									key={index}
									className="text-center"
									variants={scaleIn}
								>
									<div className={`text-2xl lg:text-3xl font-bold mb-1 ${stat.color}`}>
										<CountUpNumber end={stat.number} className={`text-2xl lg:text-3xl font-bold ${stat.color}`} />
									</div>
									<div className="text-sm text-gray-500 font-medium">{stat.label}</div>
								</motion.div>
							))}
						</motion.div>
					</div>
				</div>
			</motion.section>


			{/* 专利证书展示 - 大疆风格 */}
			<motion.section
				className="py-12 lg:py-16 bg-gray-50"
				initial="hidden"
				whileInView="visible"
				viewport={{ once: true, amount: 0.2 }}
				variants={staggerContainer}
			>
				<div className="container mx-auto px-4 sm:px-6 lg:px-8">
					<motion.div className="text-center mb-8 lg:mb-12" variants={fadeInUp}>
						<div className="inline-block px-3 py-1 bg-white rounded-full mb-3 shadow-sm">
							<span className="text-gray-600 text-xs font-medium uppercase tracking-wide">
								{t("patent.patentTechSubtitle")}
							</span>
						</div>
						<h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 tracking-tight">
							{t("patent.patentTechTitle")}
						</h2>
						<p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
							{t("patent.patentTechDescription")}
						</p>
					</motion.div>

					{/* 专利证书网格 - 苹果风格 */}
					<motion.div
						className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 mb-12"
						variants={staggerContainer}
					>
						{patentImages.slice(0, 6).map((image, index) => (
							<motion.div
								key={index}
								className="group cursor-pointer"
								variants={scaleIn}
								whileHover={{ y: -4 }}
								transition={{ duration: 0.2 }}
							>
								<div className="relative aspect-[4/3] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shadow-sm group-hover:shadow-lg transition-all duration-300">
									{/* 占位图片 */}
									<div className="w-full h-full flex items-center justify-center">
										<div className="text-gray-400 text-center">
											<svg className="w-12 h-12 mx-auto mb-3" fill="currentColor" viewBox="0 0 20 20">
												<path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
											</svg>
											<span className="text-sm font-medium">{t("patent.patentCertificate")} {index + 1}</span>
										</div>
									</div>

									{/* 悬停效果 */}
									<div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 rounded-2xl"></div>
								</div>

								{/* 证书标题 */}
								<div className="mt-3 text-center">
									<h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
										{patentDetails[index % patentDetails.length]?.title || `${t("patent.patentCertificate")} ${index + 1}`}
									</h4>
									<p className="text-xs text-gray-500 mt-1">
										{patentDetails[index % patentDetails.length]?.type || t("patent.inventionPatents")}
									</p>
								</div>
							</motion.div>
						))}
					</motion.div>
				</div>
			</motion.section>

			{/* 认证证书展示 */}
			<motion.section
				className="py-12 lg:py-16 bg-white"
				initial="hidden"
				whileInView="visible"
				viewport={{ once: true, amount: 0.2 }}
				variants={staggerContainer}
			>
				<div className="container mx-auto px-4 sm:px-6 lg:px-8">
					<motion.div className="text-center mb-8 lg:mb-12" variants={fadeInUp}>
						<div className="inline-block px-3 py-1 bg-gray-100 rounded-full mb-3">
							<span className="text-gray-600 text-xs font-medium uppercase tracking-wide">
								{t("patent.certificationSubtitle")}
							</span>
						</div>
						<h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 tracking-tight">
							{t("patent.certificationTitle")}
						</h2>
						<p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
							{t("patent.certificationDescription")}
						</p>
					</motion.div>

					{/* 认证证书轮播 - 简洁版 */}
					<motion.div variants={fadeInUp}>
						<Swiper
							modules={[Autoplay, Navigation, Pagination]}
							spaceBetween={24}
							slidesPerView={1}
							autoplay={{
								delay: 4000,
								disableOnInteraction: false,
							}}
							centeredSlides={true}
							navigation={false}
							// pagination={{
							// 	clickable: true,
							// 	dynamicBullets: true
							// }}
							loop={true}
							breakpoints={{
								640: { slidesPerView: 2 },
								1024: { slidesPerView: 3 },
							}}
							className=""
						>
							{certificationImages.map((image, index) => (
								<SwiperSlide key={index}>
									<div className="group cursor-pointer">
										<div className="relative aspect-[3/4] rounded-2xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 shadow-sm group-hover:shadow-md transition-all duration-300">
											{/* 占位图片 */}
											{/* <div className="w-full h-full flex items-center justify-center">
												<div className="text-gray-400 text-center">
													<svg className="w-12 h-12 mx-auto mb-3" fill="currentColor" viewBox="0 0 20 20">
														<path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
													</svg>
													<span className="text-sm font-medium">{t("patent.certificationCertificate")} {index + 1}</span>
												</div>
											</div> */}

											{/* 悬停效果 */}
											<div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-all duration-300 rounded-2xl"></div>
										</div>

										{/* 证书信息 */}
										<div className="mt-3 text-center">
											<h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
												{certificationDetails[index % certificationDetails.length]?.title || `${t("patent.certificationCertificate")} ${index + 1}`}
											</h4>
											{/* <p className="text-xs text-gray-500 mt-1">
												{certificationDetails[index % certificationDetails.length]?.issuer || "权威机构"}
											</p> */}
										</div>
									</div>
								</SwiperSlide>
							))}
						</Swiper>
					</motion.div>
				</div>
			</motion.section>

			{/* 技术优势 - 简洁版 */}
			<motion.section
				className="py-12 lg:py-16 bg-gray-50"
				initial="hidden"
				whileInView="visible"
				viewport={{ once: true, amount: 0.2 }}
				variants={staggerContainer}
			>
				<div className="container mx-auto px-4 sm:px-6 lg:px-8">
					<motion.div className="text-center mb-8 lg:mb-12" variants={fadeInUp}>
						<div className="inline-block px-3 py-1 bg-white rounded-full mb-3 shadow-sm">
							<span className="text-gray-600 text-xs font-medium uppercase tracking-wide">
								{t("patent.advantagesSubtitle")}
							</span>
						</div>
						<h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-3 tracking-tight">
							{t("patent.advantagesTitle")}
						</h2>
						<p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
							{t("patent.advantagesDescription")}
						</p>
					</motion.div>

					<motion.div
						className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8"
						variants={staggerContainer}
					>
						<motion.div className="text-center p-6 bg-white rounded-2xl shadow-sm hover:shadow-md transition-shadow" variants={scaleIn}>
							<div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto mb-4">
								<svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
									<path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
								</svg>
							</div>
							<h3 className="text-lg font-bold text-gray-900 mb-2">{t("patent.advantage1Title")}</h3>
							<p className="text-gray-600 text-sm leading-relaxed">
								{t("patent.advantage1Description")}
							</p>
						</motion.div>

						<motion.div className="text-center p-6 bg-white rounded-2xl shadow-sm hover:shadow-md transition-shadow" variants={scaleIn}>
							<div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
								<svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
									<path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
								</svg>
							</div>
							<h3 className="text-lg font-bold text-gray-900 mb-2">{t("patent.advantage2Title")}</h3>
							<p className="text-gray-600 text-sm leading-relaxed">
								{t("patent.advantage2Description")}
							</p>
						</motion.div>

						<motion.div className="text-center p-6 bg-white rounded-2xl shadow-sm hover:shadow-md transition-shadow" variants={scaleIn}>
							<div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
								<svg className="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
									<path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
								</svg>
							</div>
							<h3 className="text-lg font-bold text-gray-900 mb-2">{t("patent.advantage3Title")}</h3>
							<p className="text-gray-600 text-sm leading-relaxed">
								{t("patent.advantage3Description")}
							</p>
						</motion.div>
					</motion.div>
				</div>
			</motion.section>

			{/* 联系我们 - 简洁版 */}
			{/* <motion.section
				className="py-12 lg:py-16 bg-gray-900 text-white"
				initial="hidden"
				whileInView="visible"
				viewport={{ once: true, amount: 0.2 }}
				variants={staggerContainer}
			>
				<div className="container mx-auto px-4 sm:px-6 lg:px-8">
					<motion.div className="text-center max-w-2xl mx-auto" variants={fadeInUp}>
						<h3 className="text-2xl sm:text-3xl font-bold mb-3 tracking-tight">
							{t("patent.contactTitle")}
						</h3>
						<p className="text-gray-300 mb-6 leading-relaxed">
							{t("patent.contactDescription")}
						</p>
						<div className="flex flex-col sm:flex-row gap-3 justify-center">
							<motion.button
								className="bg-white text-gray-900 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors"
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
							>
								{t("patent.contactButton")}
							</motion.button>
							<motion.button
								className="border border-gray-600 text-white px-6 py-3 rounded-full font-medium hover:bg-gray-800 transition-colors"
								whileHover={{ scale: 1.02 }}
								whileTap={{ scale: 0.98 }}
							>
								{t("patent.downloadButton")}
							</motion.button>
						</div>
					</motion.div>
				</div>
			</motion.section> */}
		</div>
	)
}

export default PatentCertificationPage
