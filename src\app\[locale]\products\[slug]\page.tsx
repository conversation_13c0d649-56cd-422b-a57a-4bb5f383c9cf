import { SlugMyPageProps, type MyPageProps } from "@/lib/@types/base";
import { defaultLocale } from "@/config";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { fetchProductByCategoryData } from "@/lib/api/product";
import ProductList from "@/components/ProductList/ProductList";
import { getProductCategorySeo } from "@/lib/api/seo";
import { generateSeo } from "@/lib/utils/seo";

export const generateMetadata = async (props: SlugMyPageProps) => {
	const seo = await getProductCategorySeo(props);
	return generateSeo(props, {
		...seo,
		currentPath: `/products/${props.params.slug}`,
		ogType: "website",
	});
};

export default async function ProductCategory({ params }: MyPageProps) {
	const { slug, locale } = params;
	const channel = (await getChannelLanguageMap())[defaultLocale];
	const { category } = await fetchProductByCategoryData({ locale, channel, slug });

	return (
		<>
			{/* 产品列表 */}
			<ProductList slug={slug} channel={channel} locale={locale} products={category?.products} />
		</>
	);
}
