"use client";
import React, { useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';
import { Link } from "@/navigation"
import SEOOptimizedImage from '@/components/Image/SEOOptimizedImage';
import { motion, AnimatePresence } from 'framer-motion';
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import SwiperCore from 'swiper';
import 'swiper/swiper-bundle.css';
import ProductCompare from "@/components/Product/product-compare";
import { fetchProductByCategoryDataWithLimit } from "@/lib/api/product";
import { ProductListItemFragment } from "@/gql/graphql";
interface InspirationCubeLayoutProps {
  children: React.ReactNode;
  channel: string;
  locale: string;
  categoriesData: any;
  products?: any; // 添加产品数据
}

function InspirationCubeLayout({ children, channel, locale, categoriesData, products }: InspirationCubeLayoutProps) {
  const t = useTranslations();
  const [selectedCard, setSelectedCard] = useState<any>(null);
  const [cubeProducts, setCubeProducts] = useState<ProductListItemFragment[]>([]);
  const [loading, setLoading] = useState(true);

  // 产品图片映射 - 根据产品slug映射到对应的展示图片
  const productImageMap: { [key: string]: string } = {
    'cube-s1': '/image/ldc/cube S1.png',
    'cube-s2': '/image/ldc/cube S2.png',
    'cube-m1-the-ultimate-soundproof-workspace-pod': '/image/ldc/cube M1.png',
    'cube-m2-elevate-your-workspace-with-modern-design': '/image/ldc/cube M2.png',
    'cube-l1': '/image/ldc/cube L1.png',
    'cube-l2': '/image/ldc/cube L2.png',
    // 可以根据实际产品slug添加更多映射
  };

  // 获取产品展示图片
  const getProductDisplayImage = (product: ProductListItemFragment) => {
    // 使用映射的图片
    return productImageMap[product.slug]
  };

  // 获取inspiration-cube分类的产品数据
  useEffect(() => {
    const fetchCubeProducts = async () => {
      try {
        setLoading(true);
        const response = await fetchProductByCategoryDataWithLimit({
          slug: "inspiration-cube", // 使用inspiration-cube分类
          locale: locale,
          channel: channel,
          first: 50,
          after: ""
        });

        if (response?.category?.products?.edges) {
          const productList = response.category.products.edges.map((edge: any) => edge.node);
          setCubeProducts(productList);
        }
      } catch (error) {
        console.error("Error fetching cube products:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchCubeProducts();
  }, [locale, channel]);

  // 滚动控制函数 - 简化版本
  const scrollToCard = (direction: 'prev' | 'next') => {
    const container = document.getElementById('cardContainer');
    if (!container) return;

    const cardWidth = window.innerWidth >= 768 ? 384 + 24 : 320 + 16; // w-96 + gap-6 或 w-80 + gap-4
    const scrollAmount = cardWidth; // 每次滚动一个卡片的宽度

    if (direction === 'next') {
      container.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    } else {
      container.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  // 更新按钮状态
  const updateButtonStates = (container: HTMLElement) => {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (prevBtn && nextBtn) {
      // 禁用/启用左侧按钮
      if (container.scrollLeft <= 10) {
        prevBtn.setAttribute('disabled', 'true');
      } else {
        prevBtn.removeAttribute('disabled');
      }

      // 禁用/启用右侧按钮
      const maxScroll = container.scrollWidth - container.clientWidth;
      if (container.scrollLeft >= maxScroll - 10) {
        nextBtn.setAttribute('disabled', 'true');
      } else {
        nextBtn.removeAttribute('disabled');
      }
    }
  };

  // 初始化按钮状态
  useEffect(() => {
    const container = document.getElementById('cardContainer');
    if (container) {
      // 更新按钮状态
      updateButtonStates(container);
    }
  }, []);

  // 卡片数据 - 简化版本
  const cardData = [
    {
      id: 'card-1',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    },
    {
      id: 'card-2',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    },
    {
      id: 'card-3',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    },
    {
      id: 'card-4',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    },
    {
      id: 'card-5',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    },
    {
      id: 'card-6',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    },
    {
      id: 'card-7',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    },
    {
      id: 'card-8',
      title: '严丝合缝的舱体',
      subtitle: '纵享属于你的专属空间',
    }
  ];



  return (
    <>
      {/* Inspiration Cube 专属布局 */}
      <section className="min-h-screen">
        {/* 标题区域 */}
        {/* <div className="py-12 md:py-16 px-4">
          <div className="max-w-6xl mx-auto text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4"
            >
              Inspiration Cube
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-base md:text-lg text-gray-600 mb-8 md:mb-12"
            >
              探索创意的无限可能
            </motion.p>
          </div>
        </div> */}

        {/* 产品轮播区域 */}
        <div className='bg-[#fafafc] py-8'>
          <div className="container !max-w-[850px]">
            {/* 产品轮播 */}
            <motion.div
              // initial={{ opacity: 0, y: 30 }}
              // animate={{ opacity: 1, y: 0 }}
              // transition={{ duration: 0.8, delay: 0.4 }}
              className="relative"
            >
              <Swiper
                modules={[Autoplay, Navigation, Pagination]}
                spaceBetween={16}
                slidesPerView={2}
                // autoplay={{
                //   delay: 3000,
                //   disableOnInteraction: false,
                // }}
                loop={true}
                centeredSlides={false}
                // pagination={{
                //   clickable: true,
                //   bulletClass: 'swiper-pagination-bullet !bg-gray-400',
                //   bulletActiveClass: 'swiper-pagination-bullet-active !bg-gray-800',
                // }}
                navigation={{
                  nextEl: '.swiper-button-next-custom',
                  prevEl: '.swiper-button-prev-custom',
                }}
                breakpoints={{
                  320: {
                    slidesPerView: 3,
                    spaceBetween: 12,
                  },
                  640: {
                    slidesPerView: 3,
                    spaceBetween: 16,
                  },
                  768: {
                    slidesPerView: 4,
                    spaceBetween: 20,
                  },
                  1024: {
                    slidesPerView: 6,
                    spaceBetween: 24,
                  },
                }}
                className="cube-products-swiper"
              >
                {loading ? (
                  // 加载状态
                  Array.from({ length: 6 }).map((_, index) => (
                    <SwiperSlide key={`loading-${index}`} className="h-auto">
                      <div className="flex flex-col items-center justify-center">
                        <div className="w-[60px] h-[60px] bg-gray-200 rounded"></div>
                        <div className="w-16 h-3 bg-gray-200 rounded mt-2"></div>
                      </div>
                    </SwiperSlide>
                  ))
                ) : (
                  cubeProducts.map((product, index) => (
                    <SwiperSlide key={product.id} className="h-auto">
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                        className="group cursor-pointer h-full"
                      >
                        <Link href={`/product/${product.slug}`} className='group'>
                          <div className="flex flex-col items-center justify-center">
                            {/* 产品图片容器 */}
                            <div className="w-[60px] h-[60px] overflow-hidden">
                              <SEOOptimizedImage
                                src={getProductDisplayImage(product)}
                                alt={ product.name}
                                width={1000}
                                height={100}
                                className="w-full h-full object-contain  transition-transform duration-200"
                              />
                            </div>

                            {/* 产品信息 */}
                            <div className="flex-1 flex flex-col justify-center">
                              {/* 产品名称 */}
                              <h3 className="text-[12px] text-[#323232] group-hover:text-[#000] text-center mt-2 line-clamp-1">
                                {/* product.translation?.name || 不采用翻译 */}
                                ins.{product.name}
                              </h3>
                            </div>
                          </div>
                        </Link>
                      </motion.div>
                    </SwiperSlide>
                  ))
                )}
              </Swiper>

              {/* 自定义导航按钮 */}
              <div className="swiper-button-prev-custom absolute left-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8 rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200  flex">
                <svg className="w-4 h-4 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </div>
              <div className="swiper-button-next-custom absolute right-0 top-1/2 -translate-y-1/2 z-10 w-8 h-8  rounded-full  items-center justify-center cursor-pointer ransition-colors duration-200 flex">
                <svg className="w-4 h-4 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </motion.div>
          </div>
        </div>
        <div className='py-[80px] max-lg:py-[40px]'>
          <div className='flex items-center justify-between flex-wrap gap-4 gap-y-8 container mb-[80px] max-lg:mb-[40px]'>
            <h1 className='text-[80px] max-lg:text-[48px] font-semibold'>ins.Cube</h1>
            <p className='text-[28px] max-lg:text-[20px] font-semibold'>Only sound for inspiration</p>
          </div>
          <div className='bg-gray-500 h-[700px]'>
            {/* <video
              className="w-full aspect-video object-cover"
              poster="/image/about/company.png"
              preload="metadata"
              playsInline
              controls
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
              onEnded={() => setIsPlaying(false)}
            >
              <source src="/video/factory2.webm" type="video/webm" />
              <source src="/video/factory2.mp4" type="video/mp4" />
              Your browser does not support the video tag.
            </video> */}
          </div>
        </div>
        {/* 了解区域 */}
        <div className="w-full bg-white py-16 md:py-24">
          {/* 标题区域 - 对齐版心 */}
          <div className="max-w-[1680px] mx-auto px-4 mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
              来了解一下 Inspiration Cube
            </h2>
            {/* <p className="text-lg md:text-xl text-gray-600 max-w-2xl">
              探索创新设计的无限可能，每一个立方体都承载着独特的创意理念
            </p> */}
          </div>

          {/* 卡片滚动区域 */}
          <div className="relative max-2xl:px-4">
            {/* 滚动容器 */}
            <div
              id="cardContainer"
              className="overflow-x-auto overflow-y-visible scrollbar-hide"
              onScroll={(e) => updateButtonStates(e.target as HTMLElement)}
            >
              <div className="flex gap-4 md:gap-6 pb-4 py-4" style={{
                paddingLeft: 'max(0px, calc((100vw - 1680px) / 2))',
                paddingRight: 'max(0px, calc((100vw - 1680px) / 2))'
              }}>
                {/* 循环渲染卡片 */}
                {cardData.map((card, index) => (
                  <div
                    key={card.id}
                    className={`flex-shrink-0 bg-black rounded-3xl relative overflow-hidden hover:scale-[1.02] cursor-pointer transition-transform duration-300 w-[calc(100vw-6rem)] md:w-[405px] ${index === cardData.length - 1 ? 'mr-4 md:mr-6' : ''}`}
                    style={{
                      height: 'calc((100vw - 6rem) * 1.46)',
                      maxHeight: '591px'
                    }}
                    onClick={() => setSelectedCard(card)}
                  >
                    {/* 文字内容区域 */}
                    <div className="p-6 bg-black text-white">
                      <div className="text-sm font-medium text-gray-400 mb-2">{card.title}</div>
                      <h3 className="text-xl font-bold leading-tight">
                        {card.subtitle.split('\n').map((line, i) => (
                          <span key={i}>
                            {line}
                            {i < card.subtitle.split('\n').length - 1 && <br />}
                          </span>
                        ))}
                      </h3>
                    </div>

                    {/* 苹果风格的加号按钮 - 始终显示 */}
                    <div className="absolute bottom-4 right-4 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 底部导航按钮 */}
          <div className="flex justify-center items-center gap-4 mt-8">
            <button
              id="prevBtn"
              className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => scrollToCard('prev')}
            >
              <svg className="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <button
              id="nextBtn"
              className="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-full shadow-md flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              onClick={() => scrollToCard('next')}
            >
              <svg className="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
        {/* 针对灵动仓产品系列对比 - 临时显示所有产品 */}
        <div>
          <ProductCompare categorySlug={"inspiration-cube"} locale={locale} channel={channel} />
        </div>
        {/* 如果有其他产品内容，在这里显示 */}
        {/* {children && (
          <div className="max-w-6xl mx-auto px-4 pb-16">
            <div className="pt-16">
              {children}
            </div>
          </div>
        )} */}
      </section>

      {/* 详情弹出层 */}
      <AnimatePresence>
        {selectedCard && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100] flex items-center justify-center p-4"
            onClick={() => setSelectedCard(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white relative rounded-2xl max-w-[1200px] w-full max-h-[90vh] overflow-y-auto p-[74px] my-8"
              onClick={(e) => e.stopPropagation()}
            >
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedCard(null);
                }}
                className="absolute top-4 right-4 w-10 h-10 bg-[#333336] backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-opacity-80 transition-colors z-10"
              >
                <svg className="w-5 h-5 text-[#d6d6d7]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              {/* 弹出层头部 */}
              <div className="relative overflow-hidden">
                <div className="relative z-10">
                  <h3 className="text-[18px] font-medium text-[#1d1d1f] mb-8">{selectedCard.title}</h3>
                  <h4 className="text-3xl md:text-[50px] font-semibold mb-[64px] text-[#1d1d1f]">
                    一直飙一直快
                  </h4>
                </div>
              </div>

              {/* 弹出层内容 */}
              <div className="">
                <div className='py-[64px] bg-[#f5f5f7] h-[700px] mb-[50px] rounded-3xl'>
                </div>
                <div className='py-[64px] bg-[#f5f5f7] h-[700px] rounded-3xl'>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 自定义样式 */}
      <style jsx global>{`
        .cube-products-swiper .swiper-pagination {
          bottom: 0 !important;
        }

        .cube-products-swiper .swiper-pagination-bullet {
          width: 8px !important;
          height: 8px !important;
          margin: 0 4px !important;
          opacity: 0.5 !important;
        }

        .cube-products-swiper .swiper-pagination-bullet-active {
          opacity: 1 !important;
        }

        /* 移动端滚动优化 - 每个卡片占满屏幕 */
        @media (max-width: 768px) {
          #cardContainer {
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
          }

          #cardContainer > div > div {
            scroll-snap-align: start;
          }
        }

        .cube-products-swiper .swiper-slide {
          height: auto !important;
        }

        /* 隐藏滚动条 */}
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }

        /* 平滑滚动 */
        .overflow-x-auto {
          scroll-behavior: smooth;
        }

        @media (max-width: 768px) {
          .cube-products-swiper .swiper-pagination {
            bottom: -8px !important;
          }
        }
      `}</style>
    </>
  );
}

export default React.memo(InspirationCubeLayout);
