"use client";
import { getCollectionProducts, fetchProductByCategoryDataWithLimit } from "@/lib/api/product";
import { useLocale, useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import { ProductListItemFragment } from "@/gql/graphql";
import { HomeTaile } from "../Contact/ConcatPage";
import { defaultLocale } from "@/config";
import { motion } from "framer-motion";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { handlerInnerHtml } from "../BodyText";
import { filterSortDsc } from "../Product/product-card";
import Link from "next/link";
import { translateStaticProps } from "@/lib/utils/translate";
import { MinusOutlined } from '@ant-design/icons';

// Motion 动画配置
const containerVariants = {
	hidden: { opacity: 0 },
	visible: {
		opacity: 1,
		transition: {
			staggerChildren: 0.1,
			delayChildren: 0.2,
		},
	},
};

const itemVariants = {
	hidden: {
		opacity: 0,
		y: 20,
	},
	visible: {
		opacity: 1,
		y: 0,
		transition: {
			duration: 0.6,
			ease: "easeOut",
		},
	},
};

interface ComparePageProps {
	channel?: string;
	categorySlug?: string;
}

function Index({ channel = "default-channel", categorySlug = "inspiration-cube" }: ComparePageProps) {
	const t = useTranslations();
	const locale = useLocale();

	const [products, setProducts] = useState<ProductListItemFragment[]>([]);
	const [selectedProducts, setSelectedProducts] = useState<(ProductListItemFragment | null)[]>([
		null,
		null,
		null,
	]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [translatedProductInfo, setTranslatedProductInfo] = useState<{[productId: string]: any[]}>({});

	// 获取产品数据
	useEffect(() => {
		const fetchProducts = async () => {
			try {
				setLoading(true);
				// 注释掉原来的getCollectionProducts接口
				// const response = await getCollectionProducts({
				// 	slug: "inspiration-cube-series-2",
				// 	locale: locale,
				// 	channel: channel,
				// });

				// 使用新的fetchProductByCategoryDataWithLimit接口，根据categorySlug获取产品，条数设置为50
				const response = await fetchProductByCategoryDataWithLimit({
					slug: categorySlug,
					locale: locale,
					channel: channel,
					first: 50,
					after: ""
				});

				// 修改数据结构访问路径
				if (response?.category?.products?.edges) {
					const productList = response.category.products.edges.map((edge: any) => edge.node);
					setProducts(productList);

					// 设置默认选中值，确保不重复且始终有值
					const defaultSelected: ProductListItemFragment[] = [];

					if (productList.length >= 3) {
						// 有3个或以上产品，选择前3个不同的
						defaultSelected[0] = productList[0];
						defaultSelected[1] = productList[1];
						defaultSelected[2] = productList[2];
					} else if (productList.length === 2) {
						// 只有2个产品，前两个选择不同的，第3个选择第1个
						defaultSelected[0] = productList[0];
						defaultSelected[1] = productList[1];
						defaultSelected[2] = productList[0];
					} else if (productList.length === 1) {
						// 只有1个产品，3个位置都选择同一个
						defaultSelected[0] = productList[0];
						defaultSelected[1] = productList[0];
						defaultSelected[2] = productList[0];
					} else {
						// 没有产品，设置为null（这种情况应该很少见）
						return;
					}

					setSelectedProducts(defaultSelected);
					// 翻译产品特性信息
					await translateProductInfo(productList);
				}
			} catch (error) {
				setError(error as string);
				console.error("Error fetching products:", error);
			} finally {
				setLoading(false);
			}
		};
		fetchProducts();
	}, [categorySlug, locale, channel]);

	// 解析产品媒体数据
	const parseProductMedia = (mediaString: string | null): any[] => {
		if (!mediaString) return [];
		try {
			const parsed = JSON.parse(mediaString);
			return Array.isArray(parsed) ? parsed : [];
		} catch {
			return [];
		}
	};

	// 获取产品主图
	const getProductImage = (product: ProductListItemFragment) => {
		const media = parseProductMedia(product.media);
		if (media.length > 0) {
			return media[0].url;
		}
		return product.thumbnail?.url || "/placeholder-product.jpg";
	};

	// 获取产品价格
	const getProductPrice = (product: ProductListItemFragment) => {
		const price = product.pricing?.priceRange?.start?.gross;
		if (price) {
			return `${price.currency} ${price.amount}`;
		}
		return t("product.priceOnRequest");
	};

	// 获取产品名称
	const getProductName = (product: ProductListItemFragment) => {
		return product.translation?.name || product.name;
	};

	// 解析产品特性信息（原始数据）
	const getProductInfoDetailRaw = (product: ProductListItemFragment) => {
		if (!product.metadata || !Array.isArray(product.metadata)) return [];

		const infoDetailMeta = product.metadata.find((meta: any) => meta.key === 'infoDetail');
		if (!infoDetailMeta || !infoDetailMeta.value) return [];

		try {
			const infoDetail = JSON.parse(infoDetailMeta.value) as any;
			// 返回 productInfo 数组，如果不存在则返回空数组
			return infoDetail?.productInfo || [];
		} catch (error) {
			console.error('Error parsing infoDetail:', error);
			return [];
		}
	};

	// 获取翻译后的产品特性信息
	const getProductInfoDetail = (product: ProductListItemFragment) => {
		// 如果有翻译后的数据，优先使用翻译后的数据
		if (translatedProductInfo[product.id]) {
			return translatedProductInfo[product.id];
		}
		// 否则返回原始数据
		return getProductInfoDetailRaw(product);
	};

	// 翻译产品特性信息
	const translateProductInfo = async (products: ProductListItemFragment[]) => {
		if (locale === defaultLocale) return;

		const translatedData: {[productId: string]: any[]} = {};

		for (const product of products) {
			const rawInfo = getProductInfoDetailRaw(product);
			if (rawInfo.length > 0) {
				try {
					// 翻译标题和描述
					const translatedInfo = await translateStaticProps(
						rawInfo,
						['title', 'description'],
						'auto',
						locale
					);
					translatedData[product.id] = translatedInfo;
				} catch (error) {
					console.error('Error translating product info:', error);
					translatedData[product.id] = rawInfo; // 翻译失败时使用原始数据
				}
			}
		}

		setTranslatedProductInfo(translatedData);
	};

	// 选择产品处理函数
	const handleProductSelect = (index: number, productId: string) => {
		const newSelectedProducts = [...selectedProducts];
		const selectedProduct = products.find((p) => p.id === productId);

		if (!selectedProduct) return; // 如果没找到产品，直接返回

		// 如果选择的产品已经在其他位置被选中，则自动选择其他可用产品
		if (selectedProducts.some((p, i) => i !== index && p?.id === productId)) {
			// 找到第一个未被选择的产品
			const availableProduct = products.find(
				(p) => !selectedProducts.some((selected, i) => i !== index && selected?.id === p.id),
			);
			if (availableProduct) {
				newSelectedProducts[index] = availableProduct;
			}
		} else {
			newSelectedProducts[index] = selectedProduct;
		}

		setSelectedProducts(newSelectedProducts);
	};

	if (error) {
		return (
			<div className="py-20 text-center">
				<p className="text-red-500">{error}</p>
			</div>
		);
	}

	return (
		<div className="min-h-screen">
			<HomeTaile msg={t("nav.Compare")} />
			<div className="w-full bg-white py-12 md:py-16 lg:py-20">
				<div className="container">
					{/* 产品选择器 - 原生下拉 */}
					<motion.div
						className="mb-16 grid grid-cols-1 gap-8 md:grid-cols-3"
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.6, delay: 0.2 }}
					>
						{selectedProducts.map((selectedProduct, index) => (
							<div key={index} className="text-center">
								<select
									value={selectedProduct?.id || products[0]?.id || ""}
									onChange={(e) => handleProductSelect(index, e.target.value)}
									className="w-full rounded-lg border border-gray-300 bg-white p-3 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
								>
									{products.map((product) => (
										<option
											key={product.id}
											value={product.id}
											disabled={selectedProducts.some((p, i) => i !== index && p?.id === product.id)}
										>
											{getProductName(product)}
										</option>
									))}
								</select>
							</div>
						))}
					</motion.div>

					{/* 产品对比展示 - 竖向排列 */}
					<motion.div
						className="grid grid-cols-1 gap-16 sm:grid-cols-2 lg:grid-cols-3 lg:gap-8"
						variants={containerVariants}
						initial="hidden"
						animate="visible"
					>
						{selectedProducts.map((product, index) => (
							<motion.div
								key={index}
								variants={itemVariants}
								className="flex transform flex-col items-center text-center transition-all duration-300 ease-out hover:-translate-y-2 hover:scale-[1.02]"
							>
								{product ? (
									<>
										{/* 产品图片 */}
										<div className="relative mb-6 max-w-[300px] h-[300px] w-full overflow-hidden transition-transform duration-300">
											<SEOOptimizedImage
												src={getProductImage(product)}
												alt={getProductName(product)}
												width={1000}
												height={1000}
												quality={100}
												className="h-full w-full object-cover transition-transform duration-300"
											/>
										</div>

										{/* 产品信息 */}
										<div className="w-full">
											{/* 产品名称 */}
											<h3 className="mb-[30px] line-clamp-1 text-[20px] font-semibold leading-tight text-[#1d1d1f]">
												{getProductName(product)}
											</h3>

											{/* 产品描述/标语 */}
											<div
												className="mb-4 line-clamp-2 min-h-[2.5em] text-[12px] leading-relaxed text-[#1d1d1f]"
												dangerouslySetInnerHTML={{
													__html: handlerInnerHtml(
														locale == defaultLocale
															? filterSortDsc(product?.descriptionJson).sortDsc
															: filterSortDsc(product?.translation?.descriptionJson).sortDsc ||
															filterSortDsc(product?.descriptionJson).sortDsc,
													),
												}}
											></div>

											{/* 价格信息 */}
											<div className="mb-[30px] text-[17px] font-semibold text-gray-900">
												{getProductPrice(product)}
											</div>

											{/* 库存状态 */}
											{/* <div className="mb-[30px]">
                                            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                                product.variants?.some((v: any) => v.quantityAvailable > 0)
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {product.variants?.some((v: any) => v.quantityAvailable > 0)
                                                    ? t('compare.inStock') || '有库存'
                                                    : t('compare.outOfStock') || '缺货'
                                                }
                                            </span>
                                        </div> */}

											{/* 操作按钮 */}
											<div className="mb-[38px] flex items-center justify-center gap-8">
												<Link
													href={`/product/${product.slug}`}
													className="transform rounded-full bg-mainColor px-8 py-3 font-medium text-white transition-all duration-200 hover:bg-opacity-80 hover:text-white hover:shadow-lg"
												>
													{t("common.Learn_more")}
												</Link>
												<Link
													href={`/product/${product.slug}`}
													className="font-medium text-mainColor hover:text-opacity-80 transition-colors duration-200 hover:underline"
												>
													{t("common.buy")} &nbsp; &gt;
												</Link>
											</div>

											{/* 产品特性图标 - 动态渲染 */}
											<div className="pt-[38px] border-t border-gray-200">
												<div className="flex flex-col gap-8">
													{getProductInfoDetail(product).map((info: any, infoIndex: number) => (
														<div key={info.id || infoIndex} className="flex flex-col items-center text-center">
															{/* 特性图标 */}
															<div className="w-[56px] h-[56px] flex items-center justify-center transition-colors duration-200">
																{info.media && info.media.length > 0 ? (
																	// 如果有媒体文件，显示图片
																	<SEOOptimizedImage
																		src={info.media[0].url}
																		alt={info.media[0].alt || info.title}
																		width={1000}
																		height={1000}
																		className="w-full h-full object-contain"
																	/>
																) : (
																	// 如果没有媒体文件，显示默认图标
																	// <svg className="w-5 h-5 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
																	//     <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v8a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 1v6h10V5H5z" clipRule="evenodd" />
																	// </svg>
																	<div className="text-black text-2xl">
																		<MinusOutlined />
																	</div>
																)}
															</div>
															{/* 特性标题 */}
															<p className="text-[14px] text-[#1d1d1f] mt-4 mb-2  text-center leading-relaxed">
																{info?.title || ''}
															</p>
															{/* 特性描述（可选显示） */}
															{info.description && (
																<p className="text-[12px] text-[#1d1d1f]  text-center  leading-loose">
																	{info?.description || ''}
																</p>
															)}
														</div>
													))}

													{/* 如果没有特性信息，显示默认内容 */}
													{/* {getProductInfoDetail(product).length === 0 && (
                                            <div className="flex flex-col items-center text-center">
                                                <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center mb-2">
                                                    <svg className="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v8a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm2 1v6h10V5H5z" clipRule="evenodd" />
                                                    </svg>
                                                </div>
                                                <span className="text-xs text-gray-400 font-medium">{t('product.noFeatures')}</span>
                                            </div>
                                        )} */}
												</div>
											</div>
										</div>
									</>
								) : (
									<div className="py-20 text-center">
										<div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
											<svg
												className="h-8 w-8 text-gray-400"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth={2}
													d="M12 6v6m0 0v6m0-6h6m-6 0H6"
												/>
											</svg>
										</div>
										<p className="text-gray-500">{t("common.noProductSelected")}</p>
									</div>
								)}
							</motion.div>
						))}
					</motion.div>
				</div>
			</div>
		</div>
	);
}

export default React.memo(Index);
