import { type Blog } from "@/lib/@types/api/blog";
import {defaultLocale} from "@/config";


// 获取博客分类
export const getBlogCls=async (params: Blog.GetBlogClsParams): Promise<Blog.BlogCl[]> => {
    if(!params.lang_code.lang_code)params.lang_code.lang_code=defaultLocale
    const url = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/cls_filter`;
    const res = (await fetch(url, {
        method: "POST",
        body: JSON.stringify({
            ...params,
        }),
        headers: {
            "Content-Type": "application/json"
        },
        next:{ revalidate: 60000 }
    }).then((r) => r.json())) as Blog.GetBlogClsResp;
    if (res.code === 200) {
        return res.detail.cls_list;
    } else {
      return {
        // @ts-ignore
        detail:{
          cls_list:[]
        }
      }
    }
};
// 获取博标签
export const getBlogTags=async (params: Blog.GetBlogClsParams): Promise<Blog.BlogTag[]> => {
    if(!params.lang_code.lang_code)params.lang_code.lang_code=defaultLocale

    const url = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/tag/${params.lang_code.lang_code}`;
    const res = (await fetch(url, {
        method: "GET",
        headers: {
            "Content-Type": "application/json"
        },
        next:{ revalidate: 60000 }
    }).then((r) => r.json())) as Blog.GetBlogTagResp;
    if (res.code === 200) {
        return res.detail.tag_list;
    } else {
      return {
        // @ts-ignore
       detail:{
         tag_list:[]
       }

     }
    }
};

// 获取博客列表
export const getBlogList = async (params: Blog.GetBlogListParams): Promise<any> => {
    // const locale = params?.lang_code.lang_code === defaultLocale ? "origin" : params?.lang_code.lang_code;
    if(!params.lang_code.lang_code)params.lang_code.lang_code=defaultLocale
    const url = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/blog_filter`;
    const res = (await fetch(url, {
        method: "POST",
        body: JSON.stringify({
            ...params
        }),
        headers: {
            "Content-Type": "application/json"
        },
        next:{ revalidate: 600 }
    }).then((r) => r.json())) as Blog.GetBlogListResp;
    if (res.code === 200) {
        return res
    } else {
      return {
        detail:{
        blog_list:[]
      }}
    }
};

// 获取博客详情
export const getBlogDetail = async (slug: string, locale: string): Promise<Blog.BlogContent | null> => {
    try {
        const url=`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/blog_slug/${slug}?lang_code=${locale}`
        const res = (await fetch(url
            ,{
              next:{ revalidate: 1000 }
          }
        ).then((r) => r.json())) as Blog.GetBlogDetailResp;
        if (res.code === 200) {
            return res.detail.blog_content;
        } else {
            return null;
        }
    } catch (e) {
        console.log(e);
        return null;
    }
};


export const getBlogSlug = async (params: {page:number,amount:number}): Promise<Blog.getBlogSlugResp> => {
    const url = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/get_blog_slug?page=${params.page}&amount=${params.amount}`;
    const res = (await fetch(url, {
        method: "GET",
        headers: {
            "Content-Type": "application/json"
        },
        next:{ revalidate: 600 }
    }).then((r) => r.json())) as Blog.getBlogSlugResp;
    if (res.code === 200) {
        return res
    } else {
        throw new Error(res.message);
    }
};

export  const getBlogComment=async (page:number,limit:number,blog_id:number)=>{
    const url = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/comment?page=${page}&limit=${limit}&blog_id=${blog_id}`;
    const res = (await fetch(url, {
        method: "GET",
        headers: {
            "Content-Type": "application/json"
        },
        next:{ revalidate: 600 }
    }).then((r) => r.json())) as Blog.GetBlogCommentResp;
    if (res.code === 200) {
        return res
    } else {
        throw new Error(res.message);
    }
}

export const SubmitBlogComment=async (data:Blog.BlogCommentItem)=>{
    const url = `${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/blog/comment`;
    const res = (await fetch(url, {
        method: "POST",
        body: JSON.stringify(data),
        headers: {
            "Content-Type": "application/json"
        },
        next:{ revalidate: 600 }
    }).then((r) => r.json())) as Blog.BaseResp<any>;
    if (res.code === 200) {
        return res
    } else {
        throw new Error(res.message);
    }
}
