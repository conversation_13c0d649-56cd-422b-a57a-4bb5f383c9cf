import { NextIntlClientProvider, useMessages, useTranslations } from "next-intl"; // 导入国际化相关钩子和组件
import { App } from "antd"; // 导入Ant Design应用和配置提供者
import { getMessages, getTranslations, unstable_setRequestLocale } from "next-intl/server"; // 导入设置请求语言的函数
import Script from "next/script"; // 导入Script组件
import React from "react";
import { AntdRegistry } from "@ant-design/nextjs-registry"; // 导入Ant Design注册组件
import Channel from "../../context/channel"; // 导入Channel组件
import GlobalProvider from "../GlobalProvider";
import { locales } from "@/config"; // 导入locales配置
import Client from "@/app/[locale]/client"; // 导入Client组件
import { CategoryProvider, useCategory } from "@/context/CategoriesContext";
import TopNavOne from "@/components/Header/TopNav/TopNavOne";
import ScrollToTopButton from "@/components/Common/GoToTop";
import CustomerBtn from "@/components/Other/CustomerBtn";
import { AdvancedLoadingAnimation } from "@/components/AnimatedTextGroupProps";
import FooterMenu from "@/components/Mobile/footer-menu";
import Cookie from "@/components/Cookie";
import { executeGraphQL } from "@/lib/graphql";
import { defaultLocale, handleGraphqlLocale } from "@/lib/utils/util";
import { ProductCategoriesDocument } from "@/gql/graphql";
import Footer from "@/components/Footer/page";
import dynamic from "next/dynamic";
import NextTopLoader from "nextjs-toploader";
const ModalInquiry = dynamic(() => import("@/components/Modal/ModalInquiry"), {
	ssr: false,
	loading: () => null,
});
const ModalCustomer = dynamic(() => import("@/components/Modal/ModalCustomer"), {
	ssr: false,
	loading: () => null,
});
// 定义Props类型，用于RootLayout组件
type Props = {
	children: React.ReactNode; // 子组件
	params: {
		locale: string; // 语言参数
		page: string;
	};
};

// 生成静态参数，用于不同语言的页面
export const generateStaticParams = async () => {
	return locales.map((item) => ({ locale: item }));
};

/**
 * RootLayout组件，用于页面的根布局
 * @param props 组件的props
 * @returns 页面的根布局
 */
export default async function RootLayout(props: Props) {
	console.log("设置请求语言", props.params);
	unstable_setRequestLocale(props.params.locale); // 设置请求语言
	const messages = await getMessages();

	const { categories } = await executeGraphQL(ProductCategoriesDocument, {
		withAuth: false,
		variables: { locale: handleGraphqlLocale(props.params.locale || defaultLocale), first: 6 },
		revalidate: 60,
	});
	return (
		<GlobalProvider>
			<html lang={props.params.locale}>
				{/*流量分析脚本*/}
				<head>
					{/* 加载字体 */}
					{/* <link rel="preload" href="/fonts/Ewert-Regular.ttf" as="font" type="font/ttf" crossOrigin="anonymous" /> */}
					{/* <link rel="preload" href="/fonts/sans-21.ttf" as="font" type="font/ttf" crossOrigin="anonymous" /> */}
					<link rel="preload" href="/fonts/SourceHanSansSC-Normal-2.otf" as="font" type="font/otf" crossOrigin="anonymous" />
					{/* 定义字体 */}
					<style
						dangerouslySetInnerHTML={{
							__html: `
            @font-face {
              font-family: Ewert;
              src: url('/fonts/Ewert-Regular.ttf') format('truetype');
              font-weight: normal;
              font-style: normal;
            }
            @font-face {
              font-family: sans;
              src: url('/fonts/sans-21.ttf') format('truetype');
              font-weight: normal;
              font-style: normal;
            }
			@font-face {
              font-family: syht;
              src: url('/fonts/SourceHanSansSC-Normal-2.otf') format('truetype');
              font-weight: normal;
              font-style: normal;
            }
            .sans {
              font-family: syht,Arial , sans-serif;
            }
          `,
						}}
					/>

					<Script
						strategy="afterInteractive"
						async
						src="https://www.googletagmanager.com/gtag/js?id=G-TWHYFXD9R0"
					></Script>
					<Script strategy="afterInteractive">
						{`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-TWHYFXD9R0');
            `}
					</Script>
				</head>
				<body>
					<NextTopLoader
						color="#000000"
						height={4}
						showSpinner={false}
						shadow="0 0 10px #000000"
						easing="ease"
						speed={200}
					/>
					<AntdRegistry>
						<NextIntlClientProvider locale={props.params.locale} messages={messages}>
							<CategoryProvider locale={props.params.locale}>
								<App>
									<Channel>
										<div className="sans">
											<Client />
											<TopNavOne props=""></TopNavOne>
											<div id="header" className="w-full">
												<ModalInquiry locale={props.params.locale} />
												<ModalCustomer />
											</div>
											<main className="pt-[60px] max-lg:pt-[54px]">{props.children}</main>

											<Footer categories={categories} />
											<FooterMenu />
											<ScrollToTopButton />
											<CustomerBtn />
											<Cookie />
										</div>
									</Channel>
								</App>
							</CategoryProvider>
						</NextIntlClientProvider>
					</AntdRegistry>
				</body>
			</html>
		</GlobalProvider>
	);
}
