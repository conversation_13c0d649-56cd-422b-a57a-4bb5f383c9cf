"use client";
import React, { useState } from "react";
import { Input, Button, message, Card, Spin, Tag } from "antd";
import {
	SearchOutlined,
	FileTextOutlined,
	ClockCircleOutlined,
	InboxOutlined,
	SyncOutlined,
	RollbackOutlined,
	UndoOutlined,
	CheckCircleOutlined,
	CloseCircleOutlined,
	StopOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import { GetOrderBuyID } from "@/lib/api/Checkout";
import { parseProductMedia } from "../OrderList/OrderViews";
import { useTranslations } from "next-intl";

interface OrderAddress {
	city: string;
	cityArea: string;
	companyName: string;
	country: { code: string };
	countryArea: string;
	firstName?: string;
	lastName: string;
	phone: string;
	streetAddress1: string;
	streetAddress2: string;
}

interface OrderLine {
	id: string;
	quantity: number;
	totalPrice: {
		gross: { amount: number; currency: string };
	};
	variant: {
		name: string;
		product: {
			name: string;
			slug: string;
		};
	};
}

interface Order {
	id: string;
	status: string;
	total: {
		currency: string;
	};
	shippingAddress: OrderAddress;
	billingAddress: OrderAddress;
	lines: OrderLine[];
}

// 添加一个格式化订单号的函数
const formatOrderId = (orderId: string) => {
	// 如果是Base64格式的ID，先解码并提取最后一部分
	if (orderId.includes("Order:")) {
		const decoded = orderId.split(":")[1];
		// 只取前8位，并用横线分隔
		return (
			decoded
				.slice(0, 8)
				.toUpperCase()
				.match(/.{1,4}/g)
				?.join("-") || decoded
		);
	}
	// 如果是普通格式，直接截取前12位
	return orderId.slice(0, 12).toUpperCase();
};

function TouristOrderInquiryPage() {
	const t = useTranslations();
	const [orderId, setOrderId] = useState("");
	const [loading, setLoading] = useState(false);
	const [orderData, setOrderData] = useState<any | null>(null);

	const handleSearch = async () => {
		if (!orderId.trim()) {
			message.warning(t("order.pleaseEnterOrderId"));
			return;
		}

		setLoading(true);
		try {
			const { order } = await GetOrderBuyID(orderId.trim());
			if (order.id) {
				setOrderData(order);
			} else {
				message.error(t("order.searchFailed"));
			}
		} catch (error) {
			message.error(t("order.searchFailed"));
		} finally {
			setLoading(false);
		}
	};

	const containerVariants = {
		hidden: { opacity: 0 },
		visible: {
			opacity: 1,
			transition: {
				duration: 0.8,
				when: "beforeChildren",
				staggerChildren: 0.2,
			},
		},
	};

	const itemVariants = {
		hidden: { y: 20, opacity: 0 },
		visible: {
			y: 0,
			opacity: 1,
			transition: {
				duration: 0.5,
				ease: "easeOut",
			},
		},
	};

	const buttonVariants = {
		rest: { scale: 1 },
		hover: {
			scale: 1.02,
			transition: {
				duration: 0.2,
				type: "spring",
				stiffness: 400,
				damping: 10,
			},
		},
		tap: { scale: 0.98 },
	};

	const orderStatusMap = {
		DRAFT: {
			color: "#8c8c8c",
			bgColor: "#f5f5f5",
			text: t("order.Draft"),
			icon: <FileTextOutlined />,
		},
		UNCONFIRMED: {
			color: "#faad14",
			bgColor: "#fff7e6",
			text: t("order.Unconfirmed"),
			icon: <ClockCircleOutlined />,
		},
		UNFULFILLED: {
			color: "#fa8c16",
			bgColor: "#fff2e8",
			text: t("order.Unfulfilled"),
			icon: <InboxOutlined />,
		},
		PARTIALLY_FULFILLED: {
			color: "#1890ff",
			bgColor: "#e6f7ff",
			text: t("order.Partially Fulfilled"),
			icon: <SyncOutlined spin />,
		},
		PARTIALLY_RETURNED: {
			color: "#faad14",
			bgColor: "#fff7e6",
			text: t("order.Partially Returned"),
			icon: <RollbackOutlined />,
		},
		RETURNED: {
			color: "#ff4d4f",
			bgColor: "#fff1f0",
			text: t("order.Returned"),
			icon: <UndoOutlined />,
		},
		FULFILLED: {
			color: "#52c41a",
			bgColor: "#f6ffed",
			text: t("order.Fulfilled"),
			icon: <CheckCircleOutlined />,
		},
		CANCELED: {
			color: "#ff4d4f",
			bgColor: "#fff1f0",
			text: t("order.Canceled"),
			icon: <CloseCircleOutlined />,
		},
		EXPIRED: {
			color: "#8c8c8c",
			bgColor: "#f5f5f5",
			text: t("order.Expired"),
			icon: <StopOutlined />,
		},
	} as const;

	const getStatusTag = (status: string) => {
		const statusInfo = orderStatusMap[status as keyof typeof orderStatusMap] || {
			color: "#8c8c8c",
			bgColor: "#f5f5f5",
			text: status,
			icon: <FileTextOutlined />,
		};

		return {
			color: statusInfo.color,
			text: statusInfo.text,
			icon: statusInfo.icon,
			bgColor: statusInfo.bgColor,
		};
	};

	return (
		<motion.div
			initial="hidden"
			animate="visible"
			variants={containerVariants}
			className="relative min-h-screen w-full bg-gradient-to-b from-gray-50 to-white Tourist0rderInquiryPage"
		>
			{/* 顶部装饰 */}
			{/* <div className="absolute left-0 top-0 h-32 w-full bg-[#4e2b75] bg-gradient-to-r" /> */}

			<div className="container mx-auto flex flex-col items-center px-4 pt-20">
				<motion.div
					variants={itemVariants}
					className="w-full max-w-2xl overflow-hidden rounded-2xl
                    bg-white
                    shadow-[0_20px_60px_-10px_rgba(0,0,0,0.05)]"
				>
					{/* 顶部图案 */}
					{/* <div className="h-3 bg-[#4e2b75]  bg-gradient-to-r  " /> */}

					<div className="px-8 py-12">
						<motion.h2 variants={itemVariants} className="mb-8 text-center text-3xl font-bold text-gray-800">
							{t("order.orderInquiry")}
						</motion.h2>

						<motion.div variants={itemVariants} className="group relative mb-6">
							<Input
								size="large"
								placeholder={t("order.enterOrderId")}
								value={orderId}
								onChange={(e) => setOrderId(e.target.value)}
								prefix={<SearchOutlined className="text-gray-400" />}
								onPressEnter={handleSearch}
								className="h-[56px] rounded-2xl border-transparent bg-gray-50
                         px-6 text-lg
                         text-gray-700 transition-all
                         duration-300 placeholder:text-gray-300
                         hover:bg-gray-100/80
                         focus:!border-none"
							/>
						</motion.div>

						<motion.div variants={buttonVariants} initial="rest" whileHover="hover" whileTap="tap">
							<Button
								type="primary"
								size="large"
								block
								onClick={handleSearch}
								className="h-[56px] rounded-2xl border-none bg-[#4e2b75]
                         bg-gradient-to-r  text-lg  
                         font-medium
                         shadow-[0_2px_12px_rgba(78,43,117,0.15)]
                         transition-all duration-300
                         hover:opacity-95"
							>
								{t("order.search")}
							</Button>
						</motion.div>
					</div>
				</motion.div>

				{/* 底部提示文字 */}
				<motion.p variants={itemVariants} className="mt-6 text-center text-sm text-gray-400">
					{t("order.enterOrderIdTip")}
				</motion.p>

				{orderData && (
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						className="my-8 w-full max-w-2xl "
					>
						<Card className="overflow-hidden !rounded-2xl">
							{/* 订单状态头部 */}
							<div className="relative mb-6">
								<div className="absolute left-0 top-0 h-1 w-full" />
								<div className="flex items-center justify-between">
									<div className="">
										<div className="flex items-center gap-3">
											<h2 className="text-xl font-medium">{t("order.orderDetails")}</h2>
											<div className="max-md:hidden">
												<Tag className="m-0 font-mono " color="default">
													#{formatOrderId(orderData.id)}
												</Tag>
											</div>
										</div>

										<p className="mt-2 text-sm text-gray-500">
											{t("order.orderDate")}: {new Date().toLocaleDateString()}
										</p>
									</div>

									<div className="">
										<Tag
											icon={getStatusTag(orderData.status).icon}
											style={{
												color: getStatusTag(orderData.status).color,
												backgroundColor: getStatusTag(orderData.status).bgColor,
												border: "none",
												padding: "4px 12px",
												display: "flex",
												alignItems: "center",
												gap: "4px",
											}}
										>
											{getStatusTag(orderData.status).text}
										</Tag>
									</div>
								</div>
							</div>

							<div className="space-y-8">
								{/* 收货信息 */}
								<div className="rounded-lg bg-gray-50 p-4">
									<h3 className="mb-3 flex items-center text-gray-700">
										<svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
											/>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
											/>
										</svg>
										{t("order.shippingInfo")}
									</h3>
									<div className="space-y-2 text-sm text-gray-600">
										<p className="font-medium">
											{orderData.shippingAddress.lastName} {orderData.shippingAddress.firstName}
										</p>
										<p>{orderData.shippingAddress.phone}</p>
										<p>
											{orderData.shippingAddress.countryArea} {orderData.shippingAddress.city}{" "}
											{orderData.shippingAddress.streetAddress1} {orderData.shippingAddress.streetAddress2}
										</p>
									</div>
								</div>

								{/* 订单商品 */}
								<div>
									<div className="mb-4 flex items-center justify-between">
										<h3 className="flex items-center text-gray-700">
											<svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth={2}
													d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
												/>
											</svg>
											{t("order.items")}
										</h3>
										<span className="rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-600">
											{t("order.totalItems", { count: orderData.lines.length })}
										</span>
									</div>

									<div className="space-y-4">
										{orderData.lines.map((line) => {
											const mediaList = parseProductMedia(line.variant.product.media);
											return (
												<div
													key={line.id}
													className="mb-3 flex gap-4 rounded-lg border p-4 transition-shadow hover:shadow-md max-md:px-2"
												>
													{/* 商品图片 */}
													<div className="relative h-24 w-24 flex-shrink-0 overflow-hidden rounded-lg">
														<img
															src={mediaList?.[0]?.url || "/image/default-image.webp"}
															alt={line.variant.product.name}
															className="h-full w-full object-cover transition-transform duration-300 hover:scale-105"
														/>
													</div>

													{/* 商品信息 */}
													<div className="flex flex-1 justify-between">
														<div className="flex-1">
															<h4 className="line-clamp-2 font-medium text-gray-800 max-md:text-sm">
																{line.variant.product.name}
															</h4>
															<div className="mt-2 space-y-1">
																<p className="text-sm text-gray-500">
																	{t("order.variant")}: {line.variant.name}
																</p>
																<p className="text-sm text-gray-500">
																	{t("order.Quantity")}: {line.quantity}
																</p>
															</div>
														</div>
														<div className="ml-4 text-right max-md:hidden">
															<p className="mt-4 text-sm text-gray-500">
																{t("order.unitPrice")}: {line.totalPrice.gross.currency}{" "}
																{line.totalPrice.gross.amount}
															</p>
														</div>
													</div>
												</div>
											);
										})}
									</div>
								</div>

								{/* 订单总价 */}
								<div className="rounded-lg bg-gray-50 p-4">
									<div className="flex items-center justify-between">
										<span className="text-gray-600">{t("order.total")}</span>
										<span className="text-xl font-medium ">
											{orderData.totalCharged.currency} {orderData.totalCharged.amount}
										</span>
									</div>
								</div>
							</div>
						</Card>
					</motion.div>
				)}
			</div>

			{loading && (
				<div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm">
					<Spin size="large" />
				</div>
			)}
		</motion.div>
	);
}

export default React.memo(TouristOrderInquiryPage);
