"use client";

import { useState } from "react";
import {useRouter  } from "@/navigation";
import { SearchIcon, Loader2 } from "lucide-react";

export const SearchBar = ({ channel }: { channel: string }) => {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);

	const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
		e.preventDefault();
		const formData = new FormData(e.currentTarget);
		const search = formData.get("search") as string;
		
		if (search && search.trim().length > 0) {
			setIsLoading(true);
			router.push(`/search?query=${encodeURIComponent(search)}`);
		}
	};

	return (
		<div className="w-full">
			<h3 className="text-sm font-medium text-gray-700 mb-3">搜索产品</h3>
			<form
				onSubmit={handleSubmit}
				className="group relative flex w-full items-center"
			>
				<label className="w-full">
					<span className="sr-only">search for products</span>
					<input
						type="text"
						name="search"
						placeholder="搜索产品..."
						autoComplete="on"
						required
						disabled={isLoading}
						className="h-12 w-full rounded-full border-2 border-gray-200 bg-gray-50 px-6 py-3 pr-14 text-sm text-gray-900 placeholder:text-gray-500 transition-all duration-300 focus:border-gray-400 focus:bg-white focus:outline-none focus:ring-0 disabled:opacity-50"
					/>
				</label>
				<div className="absolute inset-y-0 right-0">
					<button
						type="submit"
						disabled={isLoading}
						className="inline-flex h-12 w-12 items-center justify-center rounded-full text-gray-500 transition-all duration-300 hover:bg-gray-100 hover:text-gray-700 focus:text-gray-700 group-invalid:pointer-events-none group-invalid:opacity-80 disabled:opacity-50"
					>
						<span className="sr-only">search</span>
						{isLoading ? (
							<Loader2 className="h-5 w-5 animate-spin" />
						) : (
							<SearchIcon aria-hidden className="h-5 w-5" />
						)}
					</button>
				</div>
			</form>
		</div>
	);
};
