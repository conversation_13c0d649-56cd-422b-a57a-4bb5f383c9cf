"use client";
import React, { useEffect, useRef, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Autoplay, Navigation, Pagination, EffectFade } from "swiper/modules";
// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/navigation";
import "swiper/css/effect-fade";
import { useLocale, useTranslations } from "next-intl";
import { defaultLocale } from "@/config";
import { useRouter } from "@/navigation";
import { containerVariants, itemVariants } from "@/lib/utils/util";
import { motion } from "framer-motion";
import Image from "next/image";
import { Link } from "@/navigation"
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";

export default function IndustryApplication() {
    const t = useTranslations("IndustryApplication");
    const locale = useLocale();

    // 定义轮播图入场动画
    const swiperAnimation = {
        hidden: { opacity: 0, y: 30 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.7,
                ease: "easeOut"
            }
        }
    };

    // 定义单个幻灯片入场动画
    const slideAnimation = {
        hidden: { opacity: 0, scale: 0.9 },
        visible: {
            opacity: 1,
            scale: 1,
            transition: {
                duration: 0.5,
                ease: "easeOut"
            }
        }
    };

    const industryApplications = [
        {
            id: 1,
            title: t("case1_tit"),
            desc: t("case1_desc"),
            image: "/image/home/<USER>/case1.png",
        },
        {
            id: 2,
            title: t("case2_tit"),
            desc: t("case2_desc"),
            image: "/image/home/<USER>/case2.png",
        },
        {
            id: 3,
            title: t("case3_tit"),
            desc: t("case3_desc"),
            image: "/image/home/<USER>/case3.png",
        },
        {
            id: 4,
            title: t("case2_tit"),
            desc: t("case2_desc"),
            image: "/image/home/<USER>/case2.png",
        },
        {
            id: 5,
            title: t("case1_tit"),
            desc: t("case1_desc"),
            image: "/image/home/<USER>/case1.png",
        },
        {
            id: 6,
            title: t("case3_tit"),
            desc: t("case3_desc"),
            image: "/image/home/<USER>/case3.png",
        },
    ];

    return (
        <section className="w-full pb-12 md:pb-24 ">
            <div className="">
                <motion.div
                    className="text-center mb-8 md:mb-10"
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                    variants={containerVariants}
                >
                    <motion.h2
                        className="text-3xl md:text-4xl font-bold text-black mt-24 mb-18 uppercase"
                        variants={itemVariants}
                    >
                        {t("title")}
                    </motion.h2>
                    {/* <motion.p
                        className="text-[#6f6f6f] max-w-2xl mx-auto text-lg max-md:text-base"
                        variants={itemVariants}
                    >
                        {t("subtitle")}
                    </motion.p> */}
                </motion.div>

                <motion.div
                    className="relative"
                    initial="hidden"
                    whileInView="visible"
                    viewport={{ once: true }}
                    variants={swiperAnimation}
                >
                    <Swiper
                        spaceBetween={16}
                        slidesPerView={1}
                        modules={[Autoplay, Navigation, Pagination]}
                        autoplay={{
                            delay: 5000,
                            disableOnInteraction: false,
                        }}
                        loop={true}
                        centeredSlides
                        // pagination={{
                        //     clickable: true,
                        //     el: '.swiper-pagination',
                        // }}
                        // navigation={{
                        //     nextEl: '.swiper-button-next',
                        //     prevEl: '.swiper-button-prev',
                        // }}
                        breakpoints={{
                            640: {
                                slidesPerView: 1.5,
                                spaceBetween: 16,
                            },
                            1024: {
                                slidesPerView: 1.8,
                                spaceBetween: 16,
                            },
                        }}
                        className="industry-swiper !overflow-visible"
                    >
                        {industryApplications.map((item, index) => (
                            <SwiperSlide key={item.id} className="h-auto">
                                <motion.div
                                    className="relative group overflow-hidden"
                                    variants={slideAnimation}
                                    custom={index}
                                >
                                    <div className="relative aspect-[4/2] w-full overflow-hidden">
                                        <SEOOptimizedImage
                                            src={item.image}
                                            alt={item.title}
                                            fill
                                            quality={100}
                                            className="object-cover transition-transform duration-700 w-full h-full"
                                        />
                                    </div>
                                    <div className="max-lg:px-4">
                                        <h3 className="text-black text-xl  font-semibold mt-4 uppercase">
                                            {item.title}
                                        </h3>
                                        <p className="text-[#9b9b9b] text-sm mt-4">
                                            {item.desc}
                                        </p>
                                    </div>
                                </motion.div>
                            </SwiperSlide>
                        ))}
                    </Swiper>
                    {/* <div className="swiper-pagination mt-6"></div>
                    <div className="swiper-button-prev !hidden md:!flex !text-gray-800 !-left-10"></div>
                    <div className="swiper-button-next !hidden md:!flex !text-gray-800 !-right-10"></div> */}
                </motion.div>

                {/* NoScript fallback - 只显示图片 */}
                <noscript>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1rem', marginTop: '2rem' }}>
                        {industryApplications.map((item) => (
                            <div key={item.id} style={{ position: 'relative', aspectRatio: '4/2', overflow: 'hidden' }}>
                                <img
                                    src={item.image}
                                    alt={item.title}
                                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                                />
                                <div style={{
                                    position: 'absolute',
                                    bottom: '0',
                                    left: '0',
                                    right: '0',
                                    padding: '1.5rem',
                                    background: 'linear-gradient(transparent, rgba(0,0,0,0.7))',
                                    color: 'white'
                                }}>
                                    <h3 style={{ fontSize: '1.5rem', fontWeight: '600', margin: '0' }}>
                                        {item.title}
                                    </h3>
                                </div>
                            </div>
                        ))}
                    </div>
                </noscript>
            </div>
        </section>
    );
}